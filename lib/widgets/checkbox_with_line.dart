import 'dart:async';

import 'package:bd/pages/baidu_index/baidu_index_logic.dart';
import 'package:bd/widgets/custom_checkbox.dart';
import 'package:flutter/material.dart';
import 'package:dotted_line/dotted_line.dart';

class CheckboxListWithExpand extends StatefulWidget {
   final List<TreeNode> data;
  final Function(bool)? onCheckedChange;
  final bool hasVerticalLine;
  final Function(String)? onCitySelected;

  const CheckboxListWithExpand({
    Key? key,
    required this.data,
    this.onCheckedChange,
    this.hasVerticalLine = false,
    this.onCitySelected,
  }) : super(key: key);

  @override
  _CheckboxListWithExpandState createState() => _CheckboxListWithExpandState();
}

class _CheckboxListWithExpandState extends State<CheckboxListWithExpand> {
  // 用来跟踪每个节点的展开状态，使用 Map 来存储每个节点的展开状态
  Map<int, bool> isExpandedMap = {};

  Timer? _timer;

  void _toggleExpand(int id) {
    setState(() {
      isExpandedMap[id] = !(isExpandedMap[id] ?? false); // 切换展开状态
    });
  }

  @override
  void initState() {
    super.initState();
    // print("object");
    // 创建一个在1秒后触发的定时器
    _timer = Timer(Duration(milliseconds: 50), () {
      setState(() {
        // 在1秒后进行一次状态更新
        // 更新状态以强制组件重新渲染
        isExpandedMap[0] = true;
      });
    });
  }

  @override
  void dispose() {
    // 销毁时取消定时器以防止内存泄漏
    _timer?.cancel();
    super.dispose();
  }

  void _expandNodesBasedOnCheckedState(List<TreeNode> nodes) {
    for (var node in nodes) {
      _expandIfChildrenChecked(node);
      if (node.children != null && node.children.isNotEmpty) {
        _expandNodesBasedOnCheckedState(node.children!);
      }
    }
  }

  void _expandIfChildrenChecked(TreeNode node) {
    // 如果任意子节点被选中，则展开该节点
    if (node.children != null && node.children.isNotEmpty) {
      bool anyChildChecked = node.children!.any((child) => child.isChecked || _hasCheckedDescendant(child));
      if (anyChildChecked) {
        isExpandedMap[node.id] = true;
      }
    }
  }

  bool _hasCheckedDescendant(TreeNode node) {
    // 递归检查是否有子节点被选中
    if (node.isChecked) return true;

    if (node.children != null && node.children.isNotEmpty) {
      return node.children!.any((child) => _hasCheckedDescendant(child));
    }

    return false;
  }

  void _onParentCheckedChange(bool isChecked, TreeNode dataItem) {
    setState(() {
      dataItem.isChecked = isChecked;
      _updateChildrenCheckedState(dataItem, isChecked);
      widget.onCitySelected?.call(dataItem.name);


    });
  }

// 递归更新子节点状态，但不每次都调用 setState
  void _updateChildrenCheckedState(TreeNode node, bool isChecked) {
    node.isChecked = isChecked; // 先更新当前节点状态

    // if (node.children != null && node.children!.isNotEmpty) {
    //   for (var child in node.children!) {
    //     child.isChecked = isChecked; // 递归更新子节点状态
    //     _updateChildrenCheckedState(child, isChecked); // 继续递归
    //   }
    // }
  }

  Widget buildItem(TreeNode dataItem) {
    bool isExpanded = isExpandedMap[dataItem.id] ?? false; // 获取节点的展开状态
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            _toggleExpand(dataItem.id);
            widget.onCitySelected?.call(dataItem.name);


          },
          child: Row(
            children: [
              _buildIconWithLine(isExpanded, dataItem),
              _buildDottedLine(),
              CustomCheckbox(
                isChecked: dataItem.isChecked,
                onChanged: (value) {
                  _onParentCheckedChange(value!, dataItem); // 更新父节点状态
                },
                label: dataItem.name,
              ),
            ],
          ),
        ),
        // 展开并且有子节点时，递归展示子节点
        if (isExpanded && dataItem.children != null)
          Padding(
            padding: const EdgeInsets.only(left: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (var child in dataItem.children!)
                  buildItem(child), // 递归地展示子节点
              ],
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    // print("object");
    _expandNodesBasedOnCheckedState(widget.data);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        for (var dataItem in widget.data)
          buildItem(dataItem), // 对每个节点调用 buildItem
      ],
    );
  }

  Widget _buildIconWithLine(bool isExpanded,TreeNode dataItem) {
    return Container(
      width: 14,
      margin: EdgeInsets.symmetric(vertical: dataItem.children.isEmpty?0:5),
      height: dataItem.children.isEmpty ? 25 : 14, // 根据情况调整高度
      alignment: Alignment.center,
      decoration: dataItem.children.isNotEmpty
          ? BoxDecoration(
        color: Colors.grey.shade200,
        border: Border.all(
          color: Colors.grey, // 边框颜色
          width: 1.0, // 边框宽度
        ),
      )
          : null,
      child: dataItem.children.isEmpty?DottedLine(
        direction: Axis.vertical, // 垂直方向的虚线
        lineThickness: 0.5,
        dashGapLength: 2,
        dashLength: 3,
      ):Icon(
        isExpanded ? Icons.remove : Icons.add,
        color: Colors.grey.shade500,
        size: 12,
      ), // 如果有垂直虚线，就不需要边框
    );
  }

  Widget _buildDottedLine() {
    return Container(
      width: 10,
      height: 14,
      margin: EdgeInsets.only(left: 2),
      alignment: Alignment.center,
      child: DottedLine(
        lineThickness: 0.5,
        dashGapLength: 2,
        dashLength: 3,
      ),
    );
  }
}
