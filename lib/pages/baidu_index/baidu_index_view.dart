// ignore_for_file: prefer_const_literals_to_create_immutables
import 'dart:math';
import 'package:bd/pages/baidu_index/log_list_view.dart';
import 'package:bd/widgets/custom_checkbox.dart';
import 'package:dio5_log/dio_log.dart';

import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/utils/toast_util.dart';
import 'package:bd/widgets/checkbox_group_selection.dart';
import 'package:bd/widgets/checkbox_with_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'baidu_index_logic.dart';
import 'package:intl/intl.dart';

class BaiduIndexPage extends StatelessWidget {
  BaiduIndexPage({Key? key}) : super(key: key);

  // final ScrollController _scrollController = ScrollController(); // 创建 ScrollController
  final logic = Get.find<BaiduIndexLogic>();
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    DioLogInterceptor.enablePrintLog = false;
    logic.httpClientUtil.dio.interceptors.add(DioLogInterceptor());
    showDebugBtn(context, btnColor: Colors.blue, btnSize: 50);
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          children: [
            // 第一列
            GetBuilder<BaiduIndexLogic>(
                id: "now",
                init: logic,
                builder: (logic) {
                  return Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 15),
                          child: Text("关键词列表:（一行一个）",
                            style: TextStyle(color: Colors.red, fontSize: 14),),
                        ),
                        TekInput(
                          width: 300,
                          hintText: '关键词',
                          controller: logic.gjcTextEditingController,
                          size: TekInputSize.areaMedium,
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                                Radius.circular(5.0)),
                            // 设置圆角半径
                            borderSide: BorderSide(
                              color: Colors.grey.shade400, // 边框颜色
                              width: 1.0, // 边框宽度
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(
                                Radius.circular(5.0)),
                            // 设置圆角半径
                            borderSide: BorderSide(
                              color: Colors.blue, // 焦点状态下的边框颜色
                              width: 2.0, // 焦点状态下的边框宽度
                            ),
                          ),
                          onChanged: (v) {
                            if (v!.isEmpty) {
                              logic.keyWords = [];
                            } else {
                              logic.keyWords = v.split("\n");
                              logic.keyWords = logic.keyWords
                                  .map((e) => e.trim()) // 去除每个字符串两端的空白字符（包括空格和换行符）
                                  .where((e) => e.isNotEmpty) // 排除空字符串
                                  .toList();
                            }
                            logic.update(["three"]);
                          },
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: TekTypography(
                                  text: "地区选择",
                                  type: TekTypographyType.bodyMedium,
                                  color: Colors.blue,
                                ),
                              ),
                              TekButton(
                                size: TekButtonSize.small,
                                type: TekButtonType.info,
                                text: "清空↑",
                                onPressed: () {
                                  logic.gjcTextEditingController.text = "";
                                  logic.keyWords = [];
                                },
                              ),
                            ],
                          ),
                        ),
                        Container(
                          height: 400,
                          // color: Colors.blue,
                          width: double.infinity,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.all(
                                  Radius.circular(5.0)),
                              // 设置圆角半径
                              border: Border.all(
                                color: Colors.grey.shade400, // 边框颜色
                                width: 1.0, // 边框宽度
                              )
                          ),
                          child: Scrollbar(
                            controller: _scrollController,
                            thumbVisibility: true,
                            child: SingleChildScrollView(
                              controller: _scrollController,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0,
                                    horizontal: 12),
                                child: CheckboxListWithExpand(
                                  data: logic.area_data,
                                  onCitySelected: (v) {
                                    logic.update(["three"]);
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 5),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: TekButton(
                                  size: TekButtonSize.small,
                                  type: TekButtonType.info,
                                  text: "勾选全部省份",
                                  onPressed: () async {
                                    for (var area_data_item in logic
                                        .area_data[0].children) {
                                      if (area_data_item.name != "上海" &&
                                          area_data_item.name != "北京" &&
                                          area_data_item.name != "天津" &&
                                          area_data_item.name != "重庆" &&
                                          area_data_item.name != "台湾" &&
                                          area_data_item.name != "香港" &&
                                          area_data_item.name != "澳门") {
                                        area_data_item.isChecked = true;
                                      }
                                    }
                                    logic.update(["now"]);
                                    logic.update(["three"]);
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              Expanded(
                                child: TekButton(
                                  size: TekButtonSize.small,
                                  type: TekButtonType.info,
                                  text: "勾选地级市",
                                  onPressed: () {
                                    logic.checkPrefectureLevelCity(
                                        logic.area_data);
                                    logic.update(["now", "three"]);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 5),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: TekButton(
                                  size: TekButtonSize.small,
                                  type: TekButtonType.info,
                                  text: "勾选县级市",
                                  onPressed: () async {
                                    logic.checkCountyLevelCities(
                                        logic.area_data);
                                    logic.update(["now", "three"]);
                                  },
                                ),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              Expanded(
                                child: TekButton(
                                  size: TekButtonSize.small,
                                  type: TekButtonType.info,
                                  text: "取消全部勾选",
                                  onPressed: () {
                                    logic.setAllNodesUncheckedInList(
                                        logic.area_data);
                                    logic.update(["now", "three"]);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
   Container(
  padding: EdgeInsets.symmetric(vertical: 5),
  width: double.infinity,
  child: TekButton(
    size: TekButtonSize.small,
    type: TekButtonType.info,
    text: "手动输入城市列表",
    onPressed: () async {
      // 显示输入对话框
      String? result = await showDialog<String>(
        context: context,
        builder: (BuildContext context) {
          String inputText = '';
          return AlertDialog(
            title: Text('输入城市名称'),
            content: Container(
              width: 400, // 设置更宽的宽度
              height: 300, // 设置更高的高度
              child: TextField(
                maxLines: 15, // 增加行数
                decoration: InputDecoration(hintText: '请输入城市名称，多个城市用换行分隔'),
                onChanged: (value) {
                  List<String> cleanCities = value
                      .split('\n')                // 按换行分割成数组
                      .where((s) => s.isNotEmpty) // 过滤空行
                      .map((s) => s.trim())       // 去除每行首尾空格
                      .toList();
                  // 转换为多行字符串
                  String formattedText = cleanCities.join('\n');
                  inputText = formattedText;
                },
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: Text('取消'),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
              TextButton(
                child: Text('确定'),
                onPressed: () {
                  Navigator.of(context).pop(inputText);
                },
              ),
            ],
          );
        },
      );

      if (result != null && result.isNotEmpty) {
        List<String> cities = result.split('\n').where((city) => city.trim().isNotEmpty).toList();
        logic.checkHandMovement(logic.area_data, cities);
        logic.update(["now", "three"]);
      }
    },
  ),
),
                        // Padding(
                        //   padding: EdgeInsets.symmetric(vertical: 5),
                        //   child: CustomCheckbox(
                        //     isChecked: false,
                        //     onChanged: (value) {},
                        //     label: "(勾选我,实现一键所有地区)",
                        //   ),
                        // )


                      ],
                    ),

                  );
                }),
            SizedBox(width: 10,),
            Expanded(flex: 1, child: GetBuilder<BaiduIndexLogic>(
              id: "two",
              init: logic,
              builder: (logic) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.symmetric(vertical: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 45,
                            child: TekButton(
                              size: TekButtonSize.medium,
                              type: TekButtonType.info,
                              text: "账号管理",
                              icon: Icon(Icons.manage_accounts, size: 20),
                              onPressed: () {
                                TekDialogs.defaultDialog(
                                  width: 800,
                                  context,
                                  content: AccountManagement(),
                                );
                              },
                            ),
                          ),
                          SizedBox(height: 12),
                          // 状态显示部分
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade200),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 3,
                                  offset: Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // 总数
                                _buildCountItem(
                                  count: logic.users.length,
                                  label: "账号",
                                  color: Colors.grey[700]!,
                                ),
                                SizedBox(width: 8),
                                // 已登录
                                _buildCountItem(
                                  count: logic.users
                                      .where((user) => user.username != "暂未登录")
                                      .length,
                                  label: "已登录",
                                  color: Colors.blue[700]!,
                                ),
                                SizedBox(width: 8),
                                // 已启用
                                _buildCountItem(
                                  count: logic.users
                                      .where((user) => user.isStart)
                                      .length,
                                  label: "已启用",
                                  color: Colors.green[700]!,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    TekTypography(
                      text: "日期范围(注意格式)",
                      type: TekTypographyType.bodyMedium,
                      color: Colors.grey,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: TekInputDateTime(
                        labelText: '选择开始日期',
                        hintText: '请选日期',
                        enabled: true,
                        filled: true,
                        firstDate: () => DateTime(2000), // 手动设置最早可选中 2000 年

                        // 设置背景颜色
                        fillColor: Colors.grey.shade50,
                        // 背景颜色
                        floatingLabelBehavior: FloatingLabelBehavior.never,
                        labelStyle: TextStyle(
                            fontSize: 14
                        ),
                        // 边框样式
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: Colors.grey.shade400, width: 1), // 边框颜色和宽度
                          borderRadius: BorderRadius.circular(2.0), // 边框圆角
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.blue, width: 1),
                          // 聚焦时边框颜色和宽度
                          borderRadius: BorderRadius.circular(2.0),
                        ),

                        // 其他属性...
                        onChanged: (dateTime) {
                          // 处理日期变化的逻辑
                          String formattedDate = DateFormat('yyyy-MM-dd')
                              .format(
                              dateTime!);
                          logic.startDate = formattedDate;
                          print("object----${logic.startDate}");
                          logic.update(["now", "three"]);
                        },
                      ),
                    ),
                    TekInputDateTime(
                      labelText: '选择结束日期',
                      hintText: '请选择结束日期',
                      enabled: true,
                      filled: true,
                      // 设置背景颜色
                      fillColor: Colors.grey.shade50,
                      firstDate: () => DateTime(2000), // 手动设置最早可选中 2000 年

                      // 背景颜色
                      floatingLabelBehavior: FloatingLabelBehavior.never,
                      labelStyle: TextStyle(
                          fontSize: 14
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(
                            color: Colors.grey.shade400, width: 1), // 边框颜色和宽度
                        borderRadius: BorderRadius.circular(2.0), // 边框圆角
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue, width: 1),
                        // 聚焦时边框颜色和宽度
                        borderRadius: BorderRadius.circular(2.0),
                      ),
                      // 其他属性...
                      onChanged: (dateTime) {
                        // DateTime dateTime = DateTime(2024, 10, 1, 0, 0, 0);
                        String formattedDate = DateFormat('yyyy-MM-dd').format(
                            dateTime!);
                        logic.endDate = formattedDate;
                        print("object----${logic.endDate}");

                        logic.update(["now", "three"]);
                      },
                    ),
                    CheckboxGroupSelection(
                      onSelectionChanged: (group, selectedOptions) {
                        logic.isAnyIndexActive = group;
                        logic.AnyIndexActives = selectedOptions;
                        print("指数模式：：：：${logic.isAnyIndexActive}");
                        print(logic.AnyIndexActives);
                        //品牌词
                        if(logic.isAnyIndexActive == "brand"){
                           logic.area_data.clear();
                           logic.area_data = [
                             TreeNode("全国", 0,[])
                           ];
                          logic.loadAreaData("ppc");
                        }else if(logic.isAnyIndexActive == "consult"){
                          logic.AnyIndexActives = [true,false,false];
                          logic.area_data.clear();
                          logic.area_data = [
                            TreeNode("全国", 0,[])
                          ];
                          logic.loadAreaData("gjc");
                        }else{
                          logic.area_data.clear();
                          logic.area_data = [
                            TreeNode("全国", 0,[])
                          ];
                          logic.loadAreaData("gjc");
                        }

                      },
                    ),

                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: TekTypography(
                        text: "（以上指数三选一）",
                        type: TekTypographyType.bodyBold,
                        color: Colors.red,
                      ),
                    ),
                    TekDivider(color: Colors.grey.shade400, height: 0.5,),
                    SizedBox(height: 12,),
                    Row(
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TekTypography(
                          text: "提取间隔(秒)：",
                          type: TekTypographyType.bodyMedium,
                          color: Colors.grey.shade600
                          ,
                        ),
                        Container(
                          // width: 45,
                          // height: 35,
                          decoration: BoxDecoration(),
                          child: TekInput(
                            width: 40,
                            maxLines: 1,
                            size: TekInputSize.areaMedium,
                            initialValue: "1",
                            isDense: true,
                            // 设置为紧凑模式，减少内边距
                            contentPadding: EdgeInsets.symmetric(
                                vertical: 7, horizontal: 5),
                            // 设置较小的内边距
                            ableSuffixIconConstraints: true,
                            textAlign: TextAlign.center,
                            // 数字居中对齐
                            textStyle: TextStyle(fontSize: 12),
                            keyboardType: TextInputType.number,
                            // 数字键盘
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              // 只允许输入数字
                              LengthLimitingTextInputFormatter(2),
                              // 限制最多输入 2 位数字
                            ],
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                  Radius.circular(2.0)),
                              // 设置圆角半径
                              borderSide: BorderSide(
                                color: Colors.grey.shade400, // 边框颜色
                                width: 0.5, // 边框宽度
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.all(
                                  Radius.circular(2.0)),
                              // 设置圆角半径
                              borderSide: BorderSide(
                                color: Colors.blue, // 焦点状态下的边框颜 色
                                width: 0.5, // 边框宽度

                              ),
                            ),
                            onChanged: (v) {
                              logic.extractionInterval = v!;
                            },
                          ),
                        ),

                      ],
                    ),
                    SizedBox(height: 12,),

                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 0),
                      child: Row(
                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: TekButton(
                              size: TekButtonSize.small,
                              type: TekButtonType.info,
                              text: "需求图谱",
                            ),
                          ),
                          SizedBox(width: 5,),
                          Expanded(
                            child: TekButton(
                              size: TekButtonSize.small,
                              type: TekButtonType.info,
                              text: "人群画像",
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      height: 40,
                      margin: EdgeInsets.symmetric(vertical: 12),
                      child: TekButton(
                          size: TekButtonSize.small,
                          type: TekButtonType.info,
                          text: "日转周/月/年/均值(一键换)"
                      ),
                    ),
                    // SizedBox(height: 12,),
                    GetBuilder<BaiduIndexLogic>(

                        init: logic,
                        id: "fileStorageOptions",
                        builder: (logic) {
                          return fileStorageOptions(
                            labels: [
                              "全部放在一个文件里",
                              "每个关键词分开存放",
                              "关键词和地区都分开存放",
                            ],
                            selectedIndex: logic.fileStorageIndex,
                            onChanged: (index) {
                              logic.fileStorageIndex = index;
                              print(logic.fileStorageIndex);
                              logic.update(['fileStorageOptions']);
                            },
                          );
                        }),
                    // TekCard(child: )
                  ],
                );
              },
            ),),
            SizedBox(width: 10,),
            Expanded(flex: 2, child: GetBuilder<BaiduIndexLogic>(
                id: "three",
                init: logic,
                builder: (logic) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(padding: EdgeInsets.symmetric(vertical: 15),
                        child: Row(
                          children: [
                            Text(
                              "提取详情：关键词：${logic.keyWords.length}个，选地区：${logic
                                  .countCheckedNodes(
                                  logic.area_data)}\n时间范围：${logic
                                  .startDate}~${logic.endDate}",
                              style: TextStyle(
                                  color: Colors.black, fontSize: 12),)
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          CustomCheckbox(
                            isChecked: logic.r,
                            onChanged:  (value) {
                              logic.r = value;

                              logic.update(['three']);

                              // _notifySelectionChange();
                            }, // 禁用其他组的复选框
                            label: "日",
                          ),
                          SizedBox(width: 10,),
                          CustomCheckbox(
                            isChecked: logic.z,
                            onChanged:  (value) {
                              logic.z = value;
                              logic.update(['three']);

                              // _notifySelectionChange();
                            }, // 禁用其他组的复选框
                            label: "周",
                          ),
                          SizedBox(width: 10,),
                          CustomCheckbox(
                            isChecked: logic.y,
                            onChanged:  (value) {
                              logic.y = value;
                              logic.update(['three']);

                              // _notifySelectionChange();
                            }, // 禁用其他组的复选框
                            label: "月",
                          ),

                          SizedBox(width: 10,),
                          CustomCheckbox(
                            isChecked: logic.n,
                            onChanged:  (value) {
                              logic.n = value;
                              logic.update(['three']);

                              // _notifySelectionChange();
                            }, // 禁用其他组的复选框
                            label: "年",
                          ),


                        ],
                      ),
                      SizedBox(height: 5,),
                      Row(
                        children: [
                          CustomCheckbox(
                            isChecked: logic.sjjz,
                            onChanged:  (value) {
                              logic.sjjz = value;

                              logic.update(['three']);

                              // _notifySelectionChange();
                            }, // 禁用其他组的复选框
                            label: "数据加总",
                          ),
                          SizedBox(width: 10,),
                          CustomCheckbox(
                            isChecked: logic.sjpj,
                            onChanged:  (value) {
                              logic.sjpj = value;
                              logic.update(['three']);

                              // _notifySelectionChange();
                            }, // 禁用其他组的复选框
                            label: "数据平均",
                          ),

                        ],
                      ),
                      // 在 Column 中添加日志记录区域
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // ... 现有的状态显示 ...

                          // 日志记录区域
                          Container(
                            margin: EdgeInsets.only(top: 12),
                            width: double.infinity,
                            height: 300,  // 增加高度，显示更多日志
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade200),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 3,
                                  offset: Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 日志标题栏
                                Container(
                                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50,
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(8),
                                      topRight: Radius.circular(8),
                                    ),
                                    border: Border(
                                      bottom: BorderSide(color: Colors.grey.shade200),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.terminal, size: 18, color: Colors.grey[700]),
                                      SizedBox(width: 8),
                                      Text(
                                          '运行日志',
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey[800],
                                          )
                                      ),
                                      Spacer(),
                                      // 添加日志计数
                                      Container(
                                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Colors.blue.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: GetBuilder<BaiduIndexLogic>(
                                          id: 'logs_count',
                                          builder: (logic) => Text(
                                            '${logic.logs.length}条',
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.blue[700],
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      // 清空按钮
                                      TekButton(
                                        size: TekButtonSize.small,
                                        type: TekButtonType.info,
                                        icon: Icon(Icons.delete_outline, size: 16),
                                        text: "清空",
                                        onPressed: () => logic.clearLogs(),
                                      ),
                                    ],
                                  ),
                                ),

                                // 日志内容区域
                                LogListView(),
                                // Expanded(
                                //   child: Container(
                                //     decoration: BoxDecoration(
                                //       color: Colors.grey.shade50,
                                //       borderRadius: BorderRadius.only(
                                //         bottomLeft: Radius.circular(8),
                                //         bottomRight: Radius.circular(8),
                                //       ),
                                //     ),
                                //     child: Scrollbar(
                                //       controller: logic.logScrollController,  // 指定控制器
                                //       thickness: 6,  // 滚动条粗细
                                //       radius: Radius.circular(3),  // 滚动条圆角
                                //       child: SingleChildScrollView(
                                //         controller: logic.logScrollController,  // 指定控制器
                                //         padding: EdgeInsets.all(16),
                                //         child: GetBuilder<BaiduIndexLogic>(
                                //             id: 'logs',
                                //             builder: (logic) {
                                //               return Column(
                                //                 crossAxisAlignment: CrossAxisAlignment.start,
                                //                 children: logic.logs.map((log) =>
                                //                     Container(
                                //                       margin: EdgeInsets.only(bottom: 8),
                                //                       padding: EdgeInsets.all(8),
                                //                       decoration: BoxDecoration(
                                //                         color: Colors.white,
                                //                         borderRadius: BorderRadius.circular(6),
                                //                         border: Border.all(
                                //                           color: log.contains('错误') ? Colors.red.shade100 :
                                //                           log.contains('成功') ? Colors.green.shade100 :
                                //                           Colors.grey.shade200,
                                //                         ),
                                //                       ),
                                //                       child: Row(
                                //                         crossAxisAlignment: CrossAxisAlignment.start,
                                //                         children: [
                                //                           // 时间戳
                                //                           Text(
                                //                             log.split(']')[0].substring(1),  // 提取时间戳
                                //                             style: TextStyle(
                                //                               fontSize: 11,
                                //                               color: Colors.grey[600],
                                //                               fontFamily: 'Consolas',
                                //                             ),
                                //                           ),
                                //                           SizedBox(width: 12),
                                //                           // 日志内容
                                //                           Expanded(
                                //                             child: Text(
                                //                               log.split(']')[1].trim(),  // 提取日志内容
                                //                               style: TextStyle(
                                //                                 fontSize: 12,
                                //                                 height: 1.4,
                                //                                 color: log.contains('错误') ? Colors.red[700] :
                                //                                 log.contains('成功') ? Colors.green[700] :
                                //                                 Colors.grey[800],
                                //                                 fontFamily: 'Consolas',
                                //                               ),
                                //                             ),
                                //                           ),
                                //                         ],
                                //                       ),
                                //                     ),
                                //                 ).toList(),
                                //               );
                                //             }
                                //         ),
                                //       ),
                                //     ),
                                //   ),
                                // ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      // SizedBox(height: 12,),
                      // Text("状态：提取成功!",
                      //   style: TextStyle(color: Colors.green, fontSize: 13),),
                      // 加入日志记录
                      SizedBox(height: 12,),

                      GetBuilder<BaiduIndexLogic>(
                          id: 'buttons',
                          builder: (logic) {
                            return Row(
                              children: [
                                TekButton(
                                  size: TekButtonSize.large,
                                  type: TekButtonType.outline,
                                  width: 80,
                                  height: 40,
                                  text: "检测",
                                  onPressed: ()=>logic.onCheckExit()
                                ),
                                SizedBox(width: 20),

                                TekButton(
                                  size: TekButtonSize.large,
                                  type: _getButtonType(logic.taskState),
                                  width: 80,
                                  height: 40,
                                  text: _getButtonText(logic.taskState),
                                  onPressed: logic.taskState == TaskState.running
                                      ? null  // 运行中禁用按钮
                                      : () {
                                    if (logic.taskState == TaskState.initial || logic.taskState == TaskState.stopped) {
                                      logic.onBdStart();
                                    } else {
                                      logic.onBdContinue();
                                    }
                                  },
                                ),
                                SizedBox(width: 20),
                                // 停止按钮
                                TekButton(
                                  width: 80,
                                  height: 40,
                                  size: TekButtonSize.large,
                                  type: TekButtonType.danger,
                                  text: "停止",
                                  onPressed: () => logic.onBdStop(),  // 非运行状态下禁用
                                ),
                                SizedBox(width: 20),
                                // 停止按钮
                                TekButton(
                                  width: 80,
                                  height: 40,
                                  // size: TekButtonType.success,
                                  type: TekButtonType.success,
                                  text: "保存数据",
                                  onPressed: (){
                                    if(logic.fileStorageIndex == 0){
                                      logic.handlingData0();
                                    }
                                    else if(logic.fileStorageIndex == 1){
                                      logic.addLog("× 错误-每个关键词分开存放功能未开放");
                                    }
                                    else if(logic.fileStorageIndex == 2){
                                      logic.handlingData2();
                                    }

                                  }
                                ),
                              ],
                            );
                          })
                    ],
                  );
                }),)
          ],
        ),
      ),
    );
  }

  //文件存放选择
  Widget fileStorageOptions({
    required List<String> labels,
    required int selectedIndex,
    required Function(int) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TekTypography(
          text: "文件存放位置",
          type: TekTypographyType.labelBold,
          color: Colors.grey,
        ),
        // SizedBox(height: 6),
        for (int i = 0; i < labels.length; i++) ...[
          SizedBox(
            height: 30,
            child: RadioListTile<int>(
              value: i,
              dense: true,
              groupValue: selectedIndex,
              contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: -4),
              // 控制内边距
              visualDensity: VisualDensity.compact,
              // 让整体更紧凑
              onChanged: (value) {
                if (value != null) {
                  onChanged(value);
                }
              },
              // contentPadding: EdgeInsets.symmetric(vertical: -10,horizontal: -10), // 控制 RadioListTile 的内边距
              title: Text(labels[i], style: TextStyle(fontSize: 13),),
            ),
          ),
        ],
      ],
    );
  }

}





class DashedLine extends StatelessWidget {
  final double dashWidth;
  final double dashSpace;
  final Color color;
  final double height;

  const DashedLine({
    Key? key,
    this.dashWidth = 4.0, // 虚线段的宽度
    this.dashSpace = 2.0, // 虚线段之间的间隔
    this.color = Colors.black, // 虚线的颜色
    this.height = 1.0, // 虚线的高度
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: DashedLinePainter(
        dashWidth: dashWidth,
        dashSpace: dashSpace,
        color: color,
      ),
      child: Container(
        height: height, // 设置虚线的高度
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final Color color;

  DashedLinePainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..strokeWidth = size.height; // 设置线的宽度

    double startX = 0.0;

    while (startX < size.width) {
      // 画线段
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(startX + dashWidth, size.height / 2),
        paint,
      );

      // 更新起始点
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false; // 不需要重新绘制
  }
}

class AccountManagement extends StatelessWidget {
  AccountManagement({super.key});

  final logic = Get.find<BaiduIndexLogic>();
  final ScrollController _scrollController = ScrollController();


  void _showBatchEnableDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('批量启用账号'),
          content: Text('确定要启用所有选中的账号吗？系统将检查每个账号是否满足启用条件。'),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () {
                // 获取所有选中的账号
                List<BaiDuUsers> selectedUsers = logic.getSelectedUsers();
                List<String> errorMessages = [];

                // 检查每个账号并启用
                for (var user in selectedUsers) {
                  if (user.isError) {
                    errorMessages.add("${user.username}: 账号request block");
                    continue;
                  }
                  if (user.cookie == null || user.cookie!.isEmpty) {
                    errorMessages.add("${user.username}: 缺少Cookie");
                    continue;
                  }
                  if (user.apiKey == null || user.apiKey!.isEmpty) {
                    errorMessages.add("${user.username}: 缺少API Key");
                    continue;
                  }
                  if (user.apiKeyTime == null || user.apiKeyTime!.isEmpty) {
                    errorMessages.add("${user.username}: 缺少API Key时间");
                    continue;
                  }
                  if (user.username == "暂未登录") {
                    errorMessages.add("有账号未登录");
                    continue;
                  }

                  // 通过验证，启用账号
                  user.isStart = true;
                }

                // 更新UI
                logic.update(['list', 'two']);

                // 关闭对话框
                Navigator.of(context).pop();

                // 显示结果
                if (errorMessages.isEmpty) {
                  showToast("所有选中账号已成功启用");
                } else {
                  // 显示错误信息
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: Text('部分账号启用失败'),
                        content: Container(
                          width: 400,
                          height: 200,
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: errorMessages.map((msg) => Padding(
                                padding: EdgeInsets.only(bottom: 8),
                                child: Text(msg),
                              )).toList(),
                            ),
                          ),
                        ),
                        actions: <Widget>[
                          TextButton(
                            child: Text('确定'),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      );
                    },
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BaiduIndexLogic>(
        id: "list",
        init: logic,
        builder: (logic) {
          return ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: 750,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 头部区域
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: TekSpacings().mainSpacing,
                    vertical: 12,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          // 全选复选框
                          Checkbox(
                            value: logic.isAllSelected,
                            onChanged: (value) => logic.toggleAllSelection(value ?? false),
                          ),
                          TekTypography(
                            text: "百度指数账号管理（${logic.users.length}）",
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          // 批量操作按钮
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "代理配置",
                            onPressed:  logic.getSelectedUsers().isEmpty
                                ? null
                                : () => _showPzProxyDialog(context),
                          ),
                          SizedBox(width: 8),
                          // 批量操作按钮
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "批量设置代理",
                            onPressed: logic.getSelectedUsers().isEmpty
                                ? null
                                : () => _showBatchProxyDialog(context),
                          ),
                          SizedBox(width: 8),
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "批量启用",
                            onPressed: logic.getSelectedUsers().isEmpty
                                ? null
                                : () => _showBatchEnableDialog(context),
                          ),
                          SizedBox(width: 8),
                          TekButton(
                            size: TekButtonSize.small,
                            type: TekButtonType.info,
                            text: "添加账号",
                            onPressed: () {
                              logic.users.add(BaiDuUsers(
                                time_str: logic.formatDateTime(DateTime.now()),
                                username: "暂未登录"
                              ));
                              logic.update(['list', "two"]);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // 账号列表
                Flexible(
                  child: Scrollbar(
                    controller: _scrollController,
                    thumbVisibility: true,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      child: Padding(
                        padding: EdgeInsets.all(TekSpacings().mainSpacing),
                        child: TekCard(
                          width: double.infinity,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              for(var i = 0; i < logic.users.length; i++)
                                Container(
                                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: logic.users[i].isError?Colors.red.shade200:Colors.transparent,
                                    border: Border(
                                      bottom: BorderSide(
                                        color: Colors.grey.shade200,
                                        width: i < logic.users.length - 1 ? 1 : 0,
                                      ),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      // 添加复选框
                                      Checkbox(
                                        value: logic.users[i].isSelected,
                                        onChanged: (value) => logic.toggleUserSelection(i, value ?? false),
                                      ),
                                      // 账户信息
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment
                                              .start,
                                          children: [
                                            Text(
                                              logic.users[i].username,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14,
                                              ),
                                            ),
                                            Row(
                                              children: [
                                                Text(
                                                  logic.users[i].time_str,
                                                  style: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 12,
                                                  ),
                                                ),

                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      // 开关控制
                                      // SizedBox(width: 100,),
                                      Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          _buildSwitch(
                                            "启用",
                                            logic.users[i].isStart,
                                                (value) {
                                              // 启用前检查必要信息
                                              if (value == true) { // 只在开启时检查
                                                // if (logic.users[i].isError ) {
                                                //   showToast("账号错误，无法开启。");
                                                //   return;
                                                // }
                                                if (logic.users[i].cookie ==
                                                    null ||
                                                    logic.users[i].cookie!
                                                        .isEmpty) {
                                                  showToast("请先登录获取Cookie");
                                                  return;
                                                }
                                                if (logic.users[i].apiKey ==
                                                    null ||
                                                    logic.users[i].apiKey!
                                                        .isEmpty) {
                                                  showToast("API Key不能为空");
                                                  return;
                                                }
                                                if (logic.users[i].apiKeyTime ==
                                                    null ||
                                                    logic.users[i].apiKeyTime!
                                                        .isEmpty) {
                                                  showToast("API Key时间不能为空");
                                                  return;
                                                }
                                                if (logic.users[i].username ==
                                                    "暂未登录") {
                                                  showToast("请先登录账号");
                                                  return;
                                                }
                                              }
                                              // 通过验证后更新状态
                                              logic.users[i].isStart = value;
                                              logic.update(['list','two']);
                                            },
                                          ),
                                          _buildSwitch(
                                            "代理",
                                            logic.users[i].isProxy,
                                                (value) {
                                              if (!logic.users[i].isStart) {
                                                if (value == true) { // 开启代理时检查
                                                  if (logic.users[i]
                                                      .proxyAddress == null ||
                                                      logic.users[i]
                                                          .proxyAddress!
                                                          .isEmpty) {
                                                    showToast("请先设置代理地址");
                                                    return;
                                                  }
                                                  if (logic.users[i]
                                                      .proxyPort == null ||
                                                      logic.users[i].proxyPort!
                                                          .isEmpty) {
                                                    showToast("请先设置代理端口");
                                                    return;
                                                  }
                                                  // 如果设置了用户名，则密码也必须设置
                                                  if ((logic.users[i]
                                                      .proxyUsername != null &&
                                                      logic.users[i]
                                                          .proxyUsername!
                                                          .isNotEmpty) &&
                                                      (logic.users[i]
                                                          .proxyPassword ==
                                                          null || logic.users[i]
                                                          .proxyPassword!
                                                          .isEmpty)) {
                                                    showToast("代理密码不能为空");
                                                    return;
                                                  }
                                                }
                                                logic.users[i].isProxy = value;
                                                logic.update(['list','two']);
                                              }
                                            },
                                            enabled: !logic.users[i]
                                                .isStart, // 账号启用时不能修改代理状态
                                          ),

                                        ],
                                      ),
                                      // 添加代理倒计时显示
                                      if (logic.users[i].isProxy &&
                                          logic.users[i].proxyStartTime !=
                                              null &&
                                          logic.users[i].proxyValidTime != null)
                                        Container(
                                          margin: EdgeInsets.only(right: 8),
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 6, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(
                                                4),
                                          ),
                                          child: GetBuilder<BaiduIndexLogic>(
                                            id: 'proxy_timer_${i}',
                                            builder: (_) {
                                              DateTime now = DateTime.now();
                                              DateTime endTime = logic.users[i]
                                                  .proxyStartTime!.add(
                                                  Duration(
                                                      minutes: logic.users[i]
                                                          .proxyValidTime!)
                                              );
                                              Duration remaining = endTime
                                                  .difference(now);

                                              // 更新倒计时
                                              if (remaining.isNegative) {
                                                return Text(
                                                  "已过期",
                                                  style: TextStyle(
                                                    color: Colors.red,
                                                    fontSize: 12,
                                                  ),
                                                );
                                              }

                                              // 格式化剩余时间
                                              String remainingStr = remaining
                                                  .inHours > 0
                                                  ? "${remaining
                                                  .inHours}h${(remaining
                                                  .inMinutes % 60)}m"
                                                  : "${remaining
                                                  .inMinutes}m${(remaining
                                                  .inSeconds % 60)}s";

                                              return Text(
                                                remainingStr,
                                                style: TextStyle(
                                                  color: Colors.blue,
                                                  fontSize: 12,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      // 操作按钮
                                      SizedBox(width: 8),
                                      TekButton(
                                        size: TekButtonSize.small,
                                        type: TekButtonType.success, // 使用绿色按钮
                                        text: logic.users[i].username == "暂未登录"
                                            ? "登录"
                                            : "重新登录",
                                        onPressed: () =>
                                            logic.loginAccount(logic.users[i]),
                                      ),
                                      SizedBox(width: 8),
                                      TekButton(
                                        // size: TekButtonType.info,
                                        size: TekButtonSize.small,
                                        type: TekButtonType.info,
                                        text: "代理设置",
                                        onPressed: () =>
                                            _showProxyDialog(
                                                context, logic.users[i]),
                                      ),
                                      SizedBox(width: 8),
                                      TekButton(
                                        // size: TekButtonType.danger,
                                        size: TekButtonSize.small,
                                        type: TekButtonType.danger,
                                        text: "删除",
                                        onPressed: () {
                                          logic.users.removeAt(i);
                                          logic.update(['list', "two"]);
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }
    );
  }

  Widget _buildSwitch(String label, bool value, Function(bool) onChanged,
      {bool enabled = true}) {
    return Container(
      margin: EdgeInsets.only(right: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 13),
          ),
          Switch(
            value: value,
            onChanged: enabled ? onChanged : null,
            activeColor: Colors.blue,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }

  void _showProxyDialog(BuildContext context, BaiDuUsers user) {
    final formKey = GlobalKey<FormState>();
    final proxyController = TextEditingController(text: user.proxyAddress);
    final portController = TextEditingController(text: user.proxyPort);
    final proxyUsernameController = TextEditingController(
        text: user.proxyUsername);
    final proxyPasswordController = TextEditingController(
        text: user.proxyPassword);

    // 定义有效时间选项（分钟）
    final List<int> validTimeOptions = [
      5,
      10,
      15,
      30,
      45,
      60,
      90,
      120,
      180,
      240,
      300,
      360
    ];
    // 默认选择60分钟或最接近的现有设置
    int selectedMinutes = user.proxyValidTime ?? 60;

    TekDialogs.defaultDialog(
      context,
      width: 400,
      content: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TekTypography(
                text: "代理设置",
                type: TekTypographyType.headline,
              ),
              SizedBox(height: 16),

              // 代理基本信息
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: TekInput(
                      labelText: "代理地址",
                      controller: proxyController,
                      hintText: "例如: 127.0.0.1",
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入代理地址';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    flex: 1,
                    child: TekInput(
                      labelText: "端口",
                      controller: portController,
                      hintText: "例如: 7890",
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入端口';
                        }
                        if (int.tryParse(value) == null) {
                          return '请输入有效端口';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),

              // 代理认证信息
              Row(
                children: [
                  Expanded(
                    child: TekInput(
                      labelText: "代理用户名",
                      controller: proxyUsernameController,
                      hintText: "可选",
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: TekInput(
                      labelText: "代理密码",
                      controller: proxyPasswordController,
                      hintText: "可选",
                      obscureText: true,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),

              // 代理有效时间下拉框
              GetBuilder<BaiduIndexLogic>(

                  id: "proxy_dialog",
                  init: logic,
                  builder: (logic) {
                    return Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "代理有效时间",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                              SizedBox(height: 4),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<int>(
                                    value: selectedMinutes,
                                    isExpanded: true,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 12),
                                    items: validTimeOptions.map((int minutes) {
                                      String displayText = minutes >= 60
                                          ? "${minutes ~/ 60}小时${minutes % 60 >
                                          0
                                          ? ' ${minutes % 60}分钟'
                                          : ''}"
                                          : "$minutes分钟";
                                      return DropdownMenuItem<int>(
                                        value: minutes,
                                        child: Text(displayText),
                                      );
                                    }).toList(),
                                    onChanged: (int? newValue) {
                                      if (newValue != null) {
                                        selectedMinutes = newValue;
                                        logic.update(['proxy_dialog']);
                                      }
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }),

              SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TekButton(
                    type: TekButtonType.danger,
                    text: "取消",
                    onPressed: () => Navigator.pop(context),
                  ),
                  SizedBox(width: 8),
                  TekButton(
                    type: TekButtonType.info,
                    text: "确认",
                    onPressed: () async {
                      if (formKey.currentState!.validate()) {
                        // 先检测代理是否可用
                        showToast("正在检测代理...");
                        bool isProxyValid = await logic.checkProxy(
                          proxyController.text,
                          portController.text,
                          proxyUsernameController.text,
                          proxyPasswordController.text,
                        );
                        dismissAllToast();

                        if (!isProxyValid) {
                          showToast("代理连接失败，请检查代理设置");
                          return;
                        }

                        // 代理可用，更新信息
                        user.proxyAddress = proxyController.text;
                        user.proxyPort = portController.text;
                        user.proxyUsername = proxyUsernameController.text;
                        user.proxyPassword = proxyPasswordController.text;
                        user.proxyValidTime = selectedMinutes;
                        user.proxyStartTime = DateTime.now();
                        user.isProxy = true;

                        // 删除原来的Future.delayed，现在由统一的定时器处理

                        logic.update(['list', 'proxy_dialog', 'now', 'three']);
                        Navigator.pop(context);
                        showToast("代理设置成功");
                        logic.addLog("✅ 账号:${user.username}---代理设置成功");
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showBatchProxyDialog(BuildContext context) {
    final textController = TextEditingController();
    final List<int> validTimeOptions = [5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    int selectedMinutes = 60;

    TekDialogs.defaultDialog(
      context,
      width: 600,
      content: GetBuilder<BaiduIndexLogic>(
        id: "proxy_dialog",
        builder: (logic) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TekTypography(
                  text: "批量导入代理",
                  type: TekTypographyType.headline,
                ),
                SizedBox(height: 16),

                // 文本输入区域
                TekInput(
                  labelText: "代理列表",
                  controller: textController,
                  maxLines: 10,
                  hintText: "每行一个代理，格式：IP:端口 或 IP:端口:用户名:密码",
                ),

                SizedBox(height: 12),

                // 添加有效期选择
                SizedBox(height: 12),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "代理有效时间",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<int>(
                          value: selectedMinutes,
                          isExpanded: true,
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          items: validTimeOptions.map((int minutes) {
                            String displayText = minutes >= 60
                                ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                : "$minutes分钟";
                            return DropdownMenuItem<int>(
                              value: minutes,
                              child: Text(displayText),
                            );
                          }).toList(),
                          onChanged: (int? newValue) {
                            if (newValue != null) {
                              selectedMinutes = newValue;
                              logic.update(['proxy_dialog']);
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TekButton(
                      type: TekButtonType.danger,
                      text: "取消",
                      onPressed: () => Navigator.pop(context),
                    ),
                    SizedBox(width: 8),
                    TekButton(
                      type: TekButtonType.info,
                      text: "确认导入",
                      onPressed: () async {
                        final lines = textController.text.split('\n')
                            .where((line) => line.trim().isNotEmpty);
                        final proxies = lines
                            .map((line) => ProxyConfig.fromString(line))
                            .where((proxy) => proxy != null)
                            .cast<ProxyConfig>()
                            .toList();
                        if (proxies.isEmpty) {
                          showToast("没有有效的代理配置");
                          return;
                        }
                        print("selectedMinutes ---${selectedMinutes}");
                        await logic.importProxies(proxies, selectedMinutes);
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ],
            ),
          );
        }
      ),
    );
  }





  void _showPzProxyDialog(BuildContext context){

    final textController = TextEditingController();

    final List<int> validTimeOptions = [1,5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];

    final List<String> validProxyOptions = ["否",'是'];

    String validProxyitem = logic.isZDProxy?"是":"否";
    textController.text = logic.pt_proxy_url;
    int selectedMinutes = 30;
    TekDialogs.defaultDialog(
      context,
      width: 600,
      content: GetBuilder<BaiduIndexLogic>(
          id: "pz_proxy_dialog",
          builder: (logic) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TekTypography(
                    text: "代理配置",
                    type: TekTypographyType.headline,
                  ),
                  SizedBox(height: 16),

                  // 文本输入区域
                  TekInput(
                    labelText: "代理链接",
                    controller: textController,
                    maxLines: 1,
                    hintText: "",
                  ),

                  SizedBox(height: 12),

                  // 添加有效期选择
                  SizedBox(height: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "代理自动切换",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: validProxyitem,
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: validProxyOptions.map((String minutes) {
                              return DropdownMenuItem<String>(
                                value: minutes,
                                child: Text(minutes),
                              );
                            }).toList(),
                            onChanged: ( newValue) {

                              if (newValue != null) {
                                validProxyitem = newValue;

                                if (validProxyitem == "否"){
                                  logic.isZDProxy = false;
                                }else{
                                  logic.isZDProxy = true;

                                }
                                logic.update(['pz_proxy_dialog']);

                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 24),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "代理有效时间",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<int>(
                            value: selectedMinutes,
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: validTimeOptions.map((int minutes) {
                              String displayText = minutes >= 60
                                  ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                  : "$minutes分钟";
                              return DropdownMenuItem<int>(
                                value: minutes,
                                child: Text(displayText),
                              );
                            }).toList(),
                            onChanged: (int? newValue) {
                              if (newValue != null) {
                                selectedMinutes = newValue;
                                logic.update(['pz_proxy_dialog']);
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TekButton(
                        type: TekButtonType.danger,
                        text: "取消",
                        onPressed: () => Navigator.pop(context),
                      ),
                      SizedBox(width: 8),
                      TekButton(
                        type: TekButtonType.info,
                        text: "确定配置",
                        onPressed: () async {
                          logic.pt_proxy_url = textController.text;

                          if (logic.pt_proxy_url.isEmpty) {
                            showToast("没有有效的代理链接");
                            return;
                          }

                          try {
                            // 获取所有选中用户
                            final selectedUsers = logic.users.where((user) => user.isSelected);

                            // 并行处理所有请求
                            final List<ProxyConfig> allProxies = await Future.wait(
                              selectedUsers.map((user) async {
                                final response = await logic.httpClientUtil.get(
                                  url: logic.pt_proxy_url,
                                );

                                return response.toString()
                                    .split('\n')
                                    .map((line) => line.trim())
                                    .where((line) => line.isNotEmpty)
                                    .map(ProxyConfig.fromString)
                                    .whereType<ProxyConfig>()
                                    .toList();
                              }),
                            ).then((results) => results.expand((list) => list).toList());

                            if (allProxies.isEmpty) {
                              showToast("未获取到有效代理");
                              return;
                            }
                            print("selectedMinutes ---${selectedMinutes}");
                            await logic.importProxies(allProxies, selectedMinutes);
                            Navigator.pop(context);
                          } catch (e) {
                            showToast("代理获取失败: ${e.toString()}");
                          }
                        },
                      )
                    ],
                  ),
                ],
              ),
            );
          }
      ),
    );
  }
}

// 添加辅助方法来构建状态项
Widget _buildStatusItem({
  required IconData icon,
  required String label,
  required String count,
  required Color color,
}) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 4),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: color,
        ),
        SizedBox(width: 2),
        Text(
          "$label:$count", // 移除空格，使用冒号
          style: TextStyle(
            fontSize: 12,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    ),
  );
}

// 分隔线构建方法
Widget _buildDivider() {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 4), // 减小间距
    child: Container(
      height: 12,
      width: 1,
      color: Colors.grey.shade300,
    ),
  );
}

// 计数项构建方法
Widget _buildCountItem({
  required int count,
  required String label,
  required Color color,
}) {
  return Container(
    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
    decoration: BoxDecoration(
      color: color.withOpacity(0.1),
      borderRadius: BorderRadius.circular(4),
    ),
    child: RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: count.toString(),
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          TextSpan(
            text: " $label",
            style: TextStyle(
              color: color,
              fontSize: 12,
            ),
          ),
        ],
      ),
    ),
  );
}

// 根据状态获取按钮类型
TekButtonType _getButtonType(TaskState state) {
  switch (state) {
    case TaskState.initial:
      return TekButtonType.info;    // 蓝色
    case TaskState.running:
      return TekButtonType.info;    // 蓝色
    case TaskState.error:
      return TekButtonType.warning; // 黄色
    case TaskState.stopped:
      return TekButtonType.info; // 绿色
  }
}

// 根据状态获取按钮文本
String _getButtonText(TaskState state) {
  switch (state) {
    case TaskState.initial:
      return "开始";
    case TaskState.running:
      return "进行中";
    case TaskState.error:
      return "继续";
    case TaskState.stopped:
      return "开始";
  }
}



