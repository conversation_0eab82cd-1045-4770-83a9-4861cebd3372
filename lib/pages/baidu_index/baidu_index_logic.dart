import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:bd/utils/notification_util.dart';
import 'package:bd/utils/store_util.dart';
import 'package:csv/csv.dart';
import 'package:bd/utils/encrypt.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/model/data_model.dart';
import 'package:bd/model/task_result_model.dart';
import 'package:bd/utils/http_client.dart';
import 'package:bd/utils/toast_util.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:puppeteer/protocol/network.dart';
import 'package:puppeteer/puppeteer.dart';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart' as diox;


import '../../utils/date.dart';
import '../../model/city_model.dart';  // 用于获取原始 JS 文件内容

// 定义任务状态枚举

enum TaskState {
  initial,    // 初始状态
  running,    // 任务进行中
  error,      // 任务出错
  stopped     // 手动停止
}

class BaiduIndexLogic extends GetxController {

  // 创建实例
  final httpClientUtil = HttpClientUtil();


  String pt_proxy_url ="";

  bool isZDProxy = false;



  bool isStarted = false;  // 只需要一个状态标记

  //关键词列表
  List<String>  keyWords = [];

  //账户列表
  List<BaiDuUsers> users = [];

  //开始日期
  String startDate = "";
  //结束日期
  String endDate = "";


  //选择什么指数
  String isAnyIndexActive = "";
  List AnyIndexActives = [];


  //提取间隔
  String extractionInterval = "";

  //文件存放
  int fileStorageIndex = 2;
  List<bool> fileStorageOptions = [false, false, false];

  //县级市
  List<String> county_level_city = [
    "西双版纳",
    "德宏",
    "怒江",
    "迪庆",
    "石河子",
    "昌吉",
    "阿克苏",
    "博尔塔拉",
    "阿勒泰",
    "喀什",
    "和田",
    "巴音郭楞",
    "伊犁",
    "塔城",
    "五家渠",
    "阿拉尔",
    "图木舒克",
    "湘西",
    "巢湖",
    "阿拉善盟",
    "锡林郭勒盟",
    "兴安盟",
    "临夏",
    "甘南",
    "万宁",
    "琼海",
    "东方",
    "五指山",
    "文昌",
    "陵水",
    "澄迈",
    "乐东",
    "临高",
    "定安",
    "昌江",
    "屯昌",
    "保亭",
    "白沙",
    "琼中",
    "黔南",
    "黔东南",
    "黔西南",
    "海西",
    "玉树",
    "青海海南",
    "海北",
    "黄南",
    "果洛",
    "甘孜",
    "阿坝",
    "神农架",
    "潜江",
    "延边",
    "仙桃",
    "恩施",
    "红河",
    "济源",
    "莱芜",
    "文山",
    "天门",
    "楚雄",
    "凉山"];

  List<TreeNode> area_data = [
    TreeNode("全国", 0,[])
  ];

  final  List<TreeNode> selected_area_data = [];

  //关键词输入框控制器
  TextEditingController gjcTextEditingController = TextEditingController();

  Future<Map<String, dynamic>> loadJsonFromAssets(String filePath) async {
    String jsonString = await rootBundle.loadString(filePath);
    return jsonDecode(jsonString);
  }

  //数据
  List<DataModel>  data = [];

  TaskState taskState = TaskState.initial;

  bool sjjz = true;
  bool sjpj = false;

  bool r = true;
  bool z = false;
  bool y = false;
  bool n = false;

  Timer? _proxyCheckTimer;
  
  @override
  void onInit() async{
    super.onInit();
    await loadAreaData("gjc");
    startProxyCheckTimer();
  }

  @override
  void onClose() {
    _proxyCheckTimer?.cancel();
    _proxyCheckTimer = null; // 显式置空
    super.onClose();
  }

  void startProxyCheckTimer() {
    _proxyCheckTimer?.cancel();
    // 每秒检查一次代理状态和更新UI
    _proxyCheckTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      final now = DateTime.now();
      
      for (int i = 0; i < users.length; i++) {
        var user = users[i];
        if (user.isProxy && user.proxyStartTime != null) {
          // 更新UI显示
          update(['proxy_timer_$i']);
          
          // 检查代理是否过期
          if (user.proxyValidTime != null) {
            final expiryTime = user.proxyStartTime!.add(Duration(minutes: user.proxyValidTime!));
            if (now.isAfter(expiryTime)) {
              user.isProxy = false;
              user.isStart = false;
              addLog("× 错误-账号:${user.username}---代理IP已过期");
              showToast("账号${user.username}的代理已过期，已自动关闭");

             if(isZDProxy){
               await autoRenewProxyForUser(user);
             }

              update(['two','logs']);
            }
          }
        }
      }
    });
  }


  Future<void> autoRenewProxyForUser(BaiDuUsers user) async {
    try {
      addLog("🚀 开始代理续期 - 账号: ${user.username}");

      // 发起代理请求
      final response = await httpClientUtil.get(
        url: pt_proxy_url,
      ).catchError((e) {
        throw Exception('代理API请求失败: $e');
      });

      // 解析并验证代理
      final proxy = _parseProxyResponse(response.toString());
      _validateProxyConfiguration(proxy);

      // 更新用户数据
      _updateUserProxyInfo(user, proxy);

      addLog("✅ 成功续期 - 账号: ${user.username} 代理: ${proxy.ip}:${proxy.port}");
    } catch (e) {
      addLog("❌ 续期失败 - 账号: ${user.username} 原因: ${e.toString()}");
      rethrow;
    }
  }

// 解析代理响应
  ProxyConfig _parseProxyResponse(String response) {
    final configs = response.split('\n')
        .map((line) => line.trim())
        .where((line) => line.isNotEmpty)
        .map(ProxyConfig.fromString)
        .whereType<ProxyConfig>()
        .toList();

    if (configs.isEmpty) throw Exception('无有效代理配置');
    return configs.first;
  }

// 代理有效性验证
  void _validateProxyConfiguration(ProxyConfig proxy) {
    final ipValid = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$').hasMatch(proxy.ip);
    final port = int.tryParse(proxy.port) ?? 0;

    if (!ipValid) throw Exception('无效IP格式: ${proxy.ip}');
    if (port <= 0 || port > 65535) throw Exception('无效端口: ${proxy.port}');
  }

// 更新用户信息
  void _updateUserProxyInfo(BaiDuUsers user, ProxyConfig proxy) {
    user
      ..proxyAddress = proxy.ip
      ..proxyPort = proxy.port
      ..proxyUsername = proxy.username
      ..proxyPassword = proxy.password
      ..proxyStartTime = DateTime.now()
      ..isProxy = true
      ..isStart = true;

  }

  Future<void> loadAreaData(String type) async {
    // 1. 加载 JSON 数据
    Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');
    provinces = provinces[type];
    Map<String, dynamic> cityShip = await loadJsonFromAssets('assets/cityShip.json');
    cityShip = cityShip[type];

    // 2. 添加省份到 area_data
    for (var entry in provinces.entries) {
      area_data[0].children.add(TreeNode(entry.value, int.parse(entry.key), [])); // 初始化 children 为 []
    }

    // 3. 添加城市到相应的省份
    for (var area_data_item in area_data[0].children) {
      List? cityShips = cityShip[area_data_item.id.toString()]; // 使用 List? 来接收可能为 null 的值

      // 检查 cityShips 是否为 null，且是否不为空
      if (cityShips != null && cityShips.isNotEmpty) {
        for (var cityShips_item in cityShips) {
          // 确保 cityShips_item 具有正确的属性
          area_data_item.children!.add(TreeNode(cityShips_item['label'], int.parse(cityShips_item['value']),[])); // 使用字典访问
        }
      }else{
        area_data_item.children=[];
      }
    }

    // 打印 children 的长度
    update(["now"]);
    // 4. 打印省份和城市
    // for (var area_data_item in area_data[0].children) {
    //   // 检查 children 是否不为空
    //   if (area_data_item.children.isNotEmpty) {
    //     for (var area_data_item_children_item in area_data_item.children) {
    //       addLog("省份----${area_data_item.name}-----城市------${area_data_item_children_item.name}");
    //     }
    //   }
    // }
  }

  void setAllNodesUnchecked(TreeNode node) {
    // 将当前节点的 isChecked 设为 false
    node.isChecked = false;

    // 递归处理所有子节点
    for (TreeNode child in node.children) {
      setAllNodesUnchecked(child);
    }
  }

  void setAllNodesUncheckedInList(List<TreeNode> nodeList) {
    for (TreeNode node in nodeList) {
      setAllNodesUnchecked(node);
    }
  }

  void checkCountyLevelCities(List<TreeNode> nodes,) async{
    Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');

    for (var node in nodes) {
      // 判断当前节点是否在县级市列表中
      if (d_city_s.contains(node.name)) {
        // 执行你需要的操作，例如将 isChecked 设置为 true
      } else {
        node.isChecked = true;




      }


      for (var entry in provinces.entries) {
        if (entry.value == node.name||  node.name == "吉林" || node.name == "全国") {
          node.isChecked = false;
          // 执行你需要的操作，例如将 isChecked 设置为 true
        }
      }

      // 如果该节点有子节点，递归处理
      if (node.children.isNotEmpty) {
        checkCountyLevelCities(node.children,);
      }
    }
  }



  void checkHandMovement(List<TreeNode> nodes,city) {
    for (var node in nodes) {
      // 判断当前节点是否在县级市列表中
      if (city.contains(node.name)) {
        // 执行你需要的操作，例如将 isChecked 设置为 true
        node.isChecked = true;
      } else {
      }

      // 如果该节点有子节点，递归处理
      if (node.children.isNotEmpty) {
        checkHandMovement(node.children,city);
      }
    }
  }

  void checkPrefectureLevelCity(List<TreeNode> nodes,) async{
    Map<String, dynamic> provinces = await loadJsonFromAssets('assets/provinces.json');
    for (var node in nodes) {
      // 判断当前节点是否在地级市列表中
      if (d_city_s.contains(node.name)) {
        // 执行你需要的操作，例如将 isChecked 设置为 true
          if(node.id == 922) {

          }else{
            node.isChecked = true;
          }

      } else {

      }


      // for (var entry in provinces.entries) {
      //   if (node.name == "吉林" || node.name == "全国") {
      //     node.isChecked = false;
      //     // 执行你需要的操作，例如将 isChecked 设置为 true
      //   }
      // }

      // 如果该节点有子节点，递归处理
      if (node.children.isNotEmpty) {
        checkPrefectureLevelCity(node.children,);
      }
    }
  }

  List<List<T>> partition<T>(List<T> list, int size) {
    List<List<T>> partitions = [];

    for (int i = 0; i < list.length; i += size) {
      partitions.add(list.sublist(i, i + size > list.length ? list.length : i + size));
    }

    return partitions;
  }

  Future<String?> pickSpreadsheetFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      dialogTitle: "请选择文件",
      type: FileType.custom,
      allowedExtensions: ["xlsx", "xls", "csv"], // 允许的表格文件类型
      allowMultiple: false,
    );

    if (result != null) {
      String? path = result.files.single.path;
      return path;
    } else {
      return null;
    }
  }

  Future<List<dynamic>> readExcelFile(String filePath) async {
    List<dynamic> firstColumnData = [];

    try {
      var bytes = File(filePath).readAsBytesSync();
      var excel = Excel.decodeBytes(bytes);

      // 输出Excel文件内容
      for (var table in excel.tables.keys) {
        var sheet = excel.tables[table]!;
        // 使用 sheet.rows 获取行数据并进行迭代处理
        for (var row in sheet.rows) {
          var value = row.isNotEmpty ? row.first?.value : '空行';
          firstColumnData.add(value); // 添加每一行的第一列数据到数组中
        }
      }
    } catch (e) {
      addLog('读取Excel文件失败：$e');
    }

    return firstColumnData;
  }

  String userApiKey ='';
  String userApiKeyTime ='';

  loginAccount(BaiDuUsers user) async {
    // 创建参数列表
    List<String> args = [];

    // 如果配置了代理，添加代理设置
    if (user.isProxy && user.proxyAddress != null && user.proxyPort != null) {
      args.add('--proxy-server=${user.proxyAddress}:${user.proxyPort}');
    }

    // 启动览器，直接使用 args
    var browser = await puppeteer.launch(
      headless: false,
      args: args,  // 直接传入参数列表
    );
    
    var page = await browser.newPage();

    // 如果配置了代理认证，设置认证信息
    if (user.isProxy && 
        user.proxyUsername != null && 
        user.proxyUsername!.isNotEmpty &&
        user.proxyPassword != null) {
      await page.authenticate(
        username: user.proxyUsername!, 
        password: user.proxyPassword!
      );
    }

    await page.setRequestInterception(true);
    // 创建 Completer 用于等待流任务完成
    Completer<void> onResuestCompleter = Completer<void>();

    // 原有的请求拦截逻辑
    page.onRequest.listen((request) async {
      if (request.url.contains('https://dlswbr.baidu.com/heicha/mm/2057/acs-2057.js')) {

        var response = await http.get(Uri.parse(request.url));
        if (response.statusCode == 200) {
          var originalJsContent = response.body;

          // String originalJsContent1 = "a8(b('0x79') + ae + '\\x5f' + eg(a2, a0, a1));";
          // String originalJsContent2 = "a8('\\x31\\x37\\x32\\x37\\x31\\x37\\x35\\x38\\x36\\x34\\x30\\x34\\x32\\x5f' + ae + '\\x5f' + eg(a2, a0, a1));";

          // 定义正则表达式
          RegExp regex1 = RegExp(r"a8\(b\('([^']+)'\)\+ae\+");
          RegExp regex2 = RegExp(r"a8\('([^']+)'\s*\+\s*ae");

          String timeKet = "";

          String modifiedJsContent = "";

          // 封装匹配逻辑
          String matchDynamicValue(String content, RegExp regex) {
            Match? match = regex.firstMatch(content);
            if (match != null) {
              return match.group(1)!; // 提取动态值
            }
            return ""; // 未找到匹配的值
          }

          // 先匹配第一个字符串
          timeKet = matchDynamicValue(originalJsContent, regex1);
          // 如果第一个字符串没有匹配到，则匹配第二个字符串
          if (timeKet.isEmpty) {
            timeKet = matchDynamicValue(originalJsContent, regex2);
            addLog("regex2----$timeKet");
            modifiedJsContent = originalJsContent.replaceFirst(
              "function ek(){",  // 查找目标函数 eg
              '''
            window['wrrtime']= '$timeKet';
            window['wrr'] = a0;
            function ek(){
          ''',  // 在函数头部插入日志
            );

          }else{
            timeKet = "b('$timeKet')";
            modifiedJsContent = originalJsContent.replaceFirst(
              "function ek(){",  // 查找目标函数 eg
              '''
            window['wrrtime']= $timeKet;
            window['wrr'] = a0;
            function ek(){
          ''',  // 在函数头部插入日志
            );

          }
          // 输出结果
          if (timeKet.isNotEmpty) {
            addLog('匹配到的动态值: $timeKet');
          } else {
            addLog('未找到匹配的值');

          }


          await request.respond(
            status: 200,
            contentType: 'application/javascript',
            body: modifiedJsContent,
          );
        } else {
          // 如果获取原始文件失败，继续请求而不1
          await request.continueRequest();
        }
      }
      else {
        // 对其他请求，继续请求
        await request.continueRequest();
      }
    });

    // 原有的响应处理逻辑
    page.onResponse.listen((response) async {
      if (response.url.contains("https://index.baidu.com/api/SearchApi/index")) {
        if (response.status == 200) {
          // 获取特定 URL 的 cookies
          List<Cookie> cookies = await page.cookies(urls: ['https://index.baidu.com/api/SearchApi/index']);
          addLog('Cookies for https://index.baidu.com: $cookies');
          for (var cookie in cookies) {
            if (cookie.name == 'BDUSS') {
              addLog('Cookie: Name = ${cookie.name}, Value = ${cookie.value}, Domain = ${cookie.domain}, Path = ${cookie.path}');
              user.cookie = "${cookie.name}=${cookie.value};";
            }

            if (cookie.name == 'ab_sr') {
              user.cookie +="${cookie.name}=${cookie.value};";
            }
            addLog(user.cookie);
          }
          // 等待元素加载
          await page.waitForSelector('.username-text');
          var username = await page.evaluate('''() => {
           const element = document.querySelector('.username-text');
           return element ? element.innerText : null; // 获取元素文本或返回 null
           }''');


          // 打印获取到的用户名
          addLog('Username: $username');
        // 判断是否被封号（根据实际响应内容判断）
          await Future.delayed(Duration(milliseconds: 200));
          final body = await response.text;
          addLog(body);
          final jsonBody = jsonDecode(body) as Map<String, dynamic>;

          // 正确字段提取
          final status = jsonBody['status'] ?? -1;
          final data = jsonBody['data']?.toString() ?? ''; // 确保为字符串
          final message = jsonBody['message']?.toString() ?? '无错误信息';

          addLog('状态码: $status');
          addLog('数据: $data');
          addLog('消息: $message');
          if (data == "" && message == "request block"){
            user.isStart = false;
            user.username = username;
            user.isError = true;

          }else{
            user.isStart = true;
            user.username = username;
            user.isError = false;
          }

          update(['list', "two"]);

          // 在此关闭浏览器
          await browser.close();
        } else {
          addLog("当前账户不可用");
        }

        // 完成后标记 Completer 完成
        if (!onResuestCompleter.isCompleted) onResuestCompleter.complete();
      }
    });

    // 访问目标网页
    await page.goto('https://index.baidu.com/v2/main/index.html#/trend/%E5%8D%8E%E4%B8%BA?words=%E5%8D%8E%E4%B8%BA');

    // 获取 wrr 和 wrrtime
    var wrr = await page.evaluate('''
      function() {
        console.log("123123123213");
        return window['wrr'];
      }
    ''');

    var wrrtime = await page.evaluate('''
      function() {
        return window['wrrtime'];
      }
    ''');


    if( wrr != ""){
      userApiKey = wrr;
      user.apiKey = wrr;

    }else{
      user.apiKey = userApiKey;
    }
    if( wrrtime != ""){
      userApiKeyTime = wrrtime;
      user.apiKeyTime = wrrtime.replaceAll('_', '');

    }else{
      user.apiKeyTime = userApiKeyTime.replaceAll('_', '');
    }

    addLog(user.apiKey);
    addLog(user.apiKeyTime);

    // 阻塞等待 onResuest 事件完成
    await onResuestCompleter.future;
  }


  String formatDateTime(DateTime dateTime) {
    final DateFormat formatter = DateFormat('yyyy年M月d日 H点m分');
    return formatter.format(dateTime);
  }


  int countCheckedNodes(List<TreeNode> nodes) {
    int count = 0;

    for (var node in nodes) {
      if (node.isChecked) {
        count += 1; // 如果当前节点被选中，则计入数量
      }
      if (node.children.isNotEmpty) {
        count += countCheckedNodes(node.children); // 递归检查子节点
      }
    }

    return count;
  }

// 遍历所有节点，将 isChecked == true 的节点放入列表中
  List<TreeNode> flattenCheckedNodes(List<TreeNode> nodes) {
    List<TreeNode> flatList = [];

    void traverse(TreeNode node) {
      if (node.isChecked) {
        flatList.add(node);
      }
      for (var child in node.children) {
        traverse(child);
      }
    }

    for (var node in nodes) {
      traverse(node);
    }

    return flatList;
  }


  // 检查是否有正在运行的任务
  bool hasRunningTask() {
    return isStarted && hasActiveRequests();
  }

  // 检是否有选择区
  bool hasSelectedAreas() {
    // 检查地区选择逻辑
    return flattenCheckedNodes(area_data).isNotEmpty;
  }

  // 检查是否有可用账号
  bool hasAvailableAccount() {
    return users.any((user) => 
      user.isStart && 
      user.username != "暂未登录" && 
      user.apiKey != null &&
      user.apiKey!.isNotEmpty
    );
  }

  // 检查是否有活跃的请求
  bool hasActiveRequests() {
    // 检查是否有正在进行的请求
    return true; // 根据实际情况实现
  }

  // 检查是否可以开始任务
  bool _checkCanStart() {
    // 检查关键词
    if (keyWords.isEmpty) {
      addLog("❌ 请先添加关键词");
      return false;
    }



    // 检查日期范围
    if (startDate.isEmpty || endDate.isEmpty) {
      addLog("❌ 请选择日期范围");
      return false;
    }

    // 将日期字符串转换为DateTime对象进行比较
    try {
      DateTime start = DateTime.parse(startDate);
      DateTime end = DateTime.parse(endDate);
      if (start.isAfter(end)) {
        addLog("❌ 开始日期不能大于结束日期");
        return false;
      }
    } catch (e) {
      addLog("❌ 日期格式错误");
      return false;
    }

    // 检查是否选择了指数类型
    if (!_hasSelectedIndexType()) {
      addLog("❌ 请至少选择一种指数类型");
      return false;
    }

    // 检查是否有选择地区
    if (flattenCheckedNodes(area_data).isEmpty) {
      addLog("❌ 请选择地区");
      return false;
    }
    
    //数据处理

    List<TreeNode> treeNode =  flattenCheckedNodes(area_data);
    List<YearDetails> yearDetails = daysInEachYear(startDate, endDate);
    data.clear();
    Map<String, DataModel> regionMap = {};
    for (var node in treeNode) {
      // 先插入城市
      var dataModel = DataModel(region: node.name, id: node.id.toString());

      // 存储到 regionMap，使用城市名作为键，便于快速访问
      regionMap[node.name] = dataModel;

      // 动态生成关键词，一个城市对应多个关键词
      for (var keyWord in keyWords) {
        // 创建一个空的年份数据
        Map<String, List<Drama>> yearData = {};

        // 根据 yearDetails 来生成每年的 Drama
        for (var yearDetail in yearDetails) {
          String yearKey = yearDetail.start.substring(0, 4); // 获取年份，如 "2023"

          // 如果年份数据为空，则初始化
          if (!yearData.containsKey(yearKey)) {
            yearData[yearKey] = [];
          }

          // 为每个关键词生成 Drama 数据
          yearData[yearKey]!.add(Drama(
            keyword: keyWord,  // 关键词
            startData: yearDetail.start,
            endData: yearDetail.end,
            isCompleted: false,
            all: [],
            pc: [],
            wise: [],
          ));
        }

        // 将生成的年份数据添加到 dataModel 的 data 中
        dataModel.data ??= {};  // 确保 data 是初始化的
        yearData.forEach((year, dramas) {
          if (dataModel.data!.containsKey(year)) {
            // 如果已有年份数据，则合并新的 drama
            dataModel.data![year]!.addAll(dramas);
          } else {
            // 否则直接添加
            dataModel.data![year] = dramas;
          }
        });
      }
    }


    // 最终数据存储在 regionMap 中，您可以根据需要处理它
     data = regionMap.values.toList();
    // // 将 DataModel 转换为 JSON 字符串并打印
    // String jsonString = json.encode(data.map((e) => e.toJson()).toList());
    // addLog(jsonString);

    // // 获取可用用户
    // final availableUsers = users.where((user) => isUserAvailable(user)).toList();
    // if (availableUsers.isEmpty) {
    //   showToast('没有可用用户');
    //   return false;
    // }



    // 检查是否有可用账号
    // if (!_hasAvailableAccount()) {
    //   showToast("请先添加并启用账号");
    //   return false;
    // }

    return true;
  }

  // 检查是否选择了指数类型
  bool _hasSelectedIndexType() {
    return AnyIndexActives.isNotEmpty;  // 假设你有一个存储选中指数类型的列表
  }

  // 检查是否选择了地区
  bool _hasSelectedAreas() {
    return area_data.any((area) => 
      area.isChecked || 
      (area.children?.any((child) => child.isChecked) ?? false)
    );
  }

  bool isUserAvailable(BaiDuUsers user) {
    // 检查用户基础条件：
    // 1. isStart 必须为 true
    // 2. cookie 不能为空
    // 3. apiKey 不能为空
    return user.isStart &&
        user.cookie.isNotEmpty &&
        user.apiKey.isNotEmpty;
  }

// 获取可用用户数量
  int getAvailableUserCount() {
    return users.where((user) => isUserAvailable(user)).length;
  }

  // 开始任务
  Future<void> onBdStart() async{
    addLog('✅ 成功️ 开始任务');
    if (!_checkCanStart()) return;
    taskState = TaskState.running;
    update(['buttons']);
    try {
      await distributeTasksToUsers();
    } catch (e) {
      addLog('任务分发失败: $e');
      taskState = TaskState.error;
    }
    
    update(['buttons']);
  }


   diox.Dio dioHttp = diox.Dio() ;


  Map<String,dynamic> ppcRegionId = {};


  ppcCheckRegionId ()async{
    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };
    addLog("$keyWords");

    for(var keyword in keyWords){
      addLog('📍 正在查找品牌词RegionId: $keyword ', );

      addLog("keyword--$keyword");

      String wordSugUrl = "https://index.baidu.com/insight/word/sug";

      var response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[keyword],"source":"pc_home"});
      addLog("response --- ${response['data']}");
      // 获取 data 中的 result 列表
      List<dynamic> result = response['data']['result'];

      // 遍历 result 列表
      for (var item in result) {
        // 检查 word 是否匹配
        if (item['word'] == keyword && item['type'] == "brand") {
          ppcRegionId[item["word"]] = item['brandInfo']['id'];
        }
      }
    }
    addLog('✅ 成功 ------查找结束开始提取品牌词-----', );

  }

  Future<void> distributeTasksToUsers() async {
    if(isAnyIndexActive == "brand"){
      await ppcCheckRegionId();
    }

    while (true) {

      addLog("taskState ---$taskState");
      if (taskState == TaskState.stopped) {
        addLog('⏹ 错误️ 任务已被停止！');
        break;
      }

      if (taskState == TaskState.error) {
        addLog('⏹ 错误️ 任务已被暂停-请检查失败原由！');
        break;
      }

      final availableUsers = users.where((user) =>
      user.isStart &&
          user.username != "暂未登录" &&
          user.apiKey != null &&
          user.apiKey!.isNotEmpty
      ).toList();

      if (availableUsers.isEmpty) {
        addLog("❌ 没有可用账号");
        setErrorState();

        return;
      }

      // startDate = "2008-01-04";

      // 收集未完成任务并按地区和年份分组
      var uncompletedTasks = collectUncompletedTasksByRegionAndYear(data);
      addLogTasksByRegionAndYear(uncompletedTasks);
      bool hasError = false;
      List<Future<void> Function()> taskFunctions = []; // 改用函数列表


       // 遍历每个可用用户分配任务
      for (var user in availableUsers) {
        addLog("user ---- $user");
        // 查找第一个有任务的地区和年份组合
        String? selectedRegion;
        String? selectedYear;
        List<TaskModel>? selectedTasks;

        // 查找可分配的任务
        for (var regionEntry in uncompletedTasks.entries) {
          for (var yearEntry in regionEntry.value.entries) {
            if (yearEntry.value.isNotEmpty) {
              selectedRegion = regionEntry.key;
              selectedYear = yearEntry.key;
              selectedTasks = yearEntry.value;
              break;
            }
          }
          if (selectedTasks != null) break;
        }

        // 如果找到可分配的任务
        if (selectedTasks != null && selectedTasks.isNotEmpty) {
          // 计算本次分配的任务数（不超过5个）
          int taskCount = 0;

          if (isAnyIndexActive == "brand"){
            taskCount = min(1, selectedTasks.length);
          }else{
            taskCount = min(5, selectedTasks.length);
          }
          var userTasks = selectedTasks.take(taskCount).toList();

          // 从未完成任务列表中移除已分配的任务
          selectedTasks.removeRange(0, taskCount);

          // 如果该地区该年份的任务已空，清理数据结构
          if (selectedTasks.isEmpty) {
            uncompletedTasks[selectedRegion]!.remove(selectedYear);
            if (uncompletedTasks[selectedRegion]!.isEmpty) {
              uncompletedTasks.remove(selectedRegion);
            }
          }

          addLog('\n📋 分配任务给用户: ${user.username}',);
          addLog('📍 地区: $selectedRegion', );
          addLog('📅 年份: $selectedYear',);
          addLog('📊 任务数: $taskCount', );

          // 处理任务
          addLog("需要处理的任务详情-----${userTasks.length}");
          addLog("地区-----$selectedRegion");
          addLog("年份-----$selectedYear");


          // 打印分配详情
          addLogUserTaskAssignments(user, userTasks);
          // 将整个任务包装在函数中
          taskFunctions.add(() async {
            try {
              if (isAnyIndexActive == "brand"){
                await ppcProcessTask(userTasks, user);
              }else{
                await processTask(userTasks, user);
              }
              addLog('✅ 地区-$selectedRegion -年份-$selectedYear - ${userTasks.map((task) => task.keyword).toList()} 任务完成');
            } catch (e) {
              // 错误处理
              hasError = true;
              setErrorState();
              addLog('❌ 任务失败: $e');
              NotificationUtil.show(
                title: '任务失败',
                body: '当前任务失败。请查看原由！',
              );

              if (e.toString().contains('cookie失效') || e.toString().contains('账号异常')) {
                user.isStart = false;
                addLog('❌ 任务失败️ 用户 ${user.username} 已被标记为不可用');
              }
              rethrow;
            }
          });


        }
      }




      // 等待当前批次所有任务完成
      if (taskFunctions.isNotEmpty) {
        addLog('\n⏳ 等待当前批次任务完成...', );
        addLog("并发处理任务-----$taskFunctions-----长度${taskFunctions.length}");
        await Future.wait(
          taskFunctions.map((fn) => fn()), // 在这里才真正执行函数
          eagerError: false,
        );
        // 添加延迟倒计时
        await delayWithCountdown(
          seconds: extractionInterval,
        );

        if (hasError) {
          addLog('❌ 检测到任务失败，停止后续任务', );
          break;
        }
      } else {
        addLog('✅ 成功️ 全部执行完成', );
        NotificationUtil.show(
          title: '任务状态',
          body: '任务已全部执行完成',
        );
        setInitialState();
        break;
      }

      // addLog(json.encode(data.map((e) => e.toJson()).toList()));


    }
  }

  // 打印用户任务分配情况
  void addLogUserTaskAssignments(BaiDuUsers user, List<TaskModel> tasks) {
    addLog('\n=== 用户任务分配详情 ===');
    addLog('👤 用户: ${user.username}');
    addLog('📋 分配任务数: ${tasks.length}');
    if (tasks.isNotEmpty) {
      addLog('📍 地区: ${tasks[0].region}');
      addLog('📅 年份: ${tasks[0].year}');
      addLog('任务列表:');
      for (var task in tasks) {
        addLog('  - ${task.keyword} (${task.drama.startData} ~ ${task.drama.endData})');
      }
    }
    addLog('=== 分配详情结束 ===\n');
  }


  // 按地区和年份收集未完成任务
  Map<String, Map<String, List<TaskModel>>> collectUncompletedTasksByRegionAndYear(List<DataModel> data) {
    // 外层Map的key是地区，内层Map的key是年份
    Map<String, Map<String, List<TaskModel>>> tasksByRegionAndYear = {};

    for (var dataModel in data) {
      if (dataModel.id == null) continue;

      // 初始化地区Map
      if (!tasksByRegionAndYear.containsKey(dataModel.region)) {
        tasksByRegionAndYear[dataModel.region!] = {};
      }

      dataModel.data?.forEach((year, dramaList) {
        // 初始化年份List
        if (!tasksByRegionAndYear[dataModel.region]!.containsKey(year)) {
          tasksByRegionAndYear[dataModel.region]![year] = [];
        }

        for (var drama in dramaList) {
          if (drama.isCompleted != true) {
            tasksByRegionAndYear[dataModel.region]![year]!.add(TaskModel(
              dataModel: dataModel,
              keyword: drama.keyword!,
              drama: drama,
              year: year,
              cityId: dataModel.id!,
              region: dataModel.region!,
            ));
          }
        }
      });
    }

    return tasksByRegionAndYear;
  }

// 打印任务分布情况
  void addLogTasksByRegionAndYear(Map<String, Map<String, List<TaskModel>>> tasksByRegionAndYear) {
    addLog('\n=== 未完成任务统计 ===');
    tasksByRegionAndYear.forEach((region, yearTasks) {
      addLog('\n🌍 地区: $region');
      yearTasks.forEach((year, tasks) {
        addLog('  📅 $year年 (共${tasks.length}个任务):');
        for (var task in tasks) {
          addLog('    - ${task.keyword} (${task.drama.startData} ~ ${task.drama.endData})');
        }
      });
    });
    addLog('\n=== 统计结束 ===\n');
  }


  String SearchApi = "https://index.baidu.com/api/SearchApi/index";
  String FeedSearchApi = "https://index.baidu.com/api/FeedSearchApi/getFeedIndex";
  String PPCApi = "https://index.baidu.com/insight/brand/queryBrandIndex";


// 单个任务处理函数
  Future<void> processTask(
     List<TaskModel> taskModel,
      BaiDuUsers user,
    ) async {


    addLog("------------------------");
    addLog("-----单个任务处理函数------");
    addLog("------------------------");



    String startData = taskModel.map((task) => task.drama.startData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String endData = taskModel.map((task) => task.drama.endData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String cityId = taskModel.map((task) => task.cityId).toSet().toString().replaceAll("{", "").replaceAll("}", "");

    List<List<String>> keywords = taskModel
        .map((task) {
      if (task.keyword.contains('+')) {
        // 如果包含 "+"，按 "+" 分割并为每个部分生成一个 Map，然后转换成 JSON 字符串
        return task.keyword.split('+').map((part) {
          var map = {'name': part.toString(), 'wordType': 1};
          return json.encode(map);  // 每个 Map 被 json.encode 转换为 JSON 字符串
        }).toList();
      } else {
        // 如果没有 "+"，直接生成一个包含一个 JSON 字符串的列表
        var map = {'name': task.keyword.toString(), 'wordType': 1};
        return [json.encode(map)];
      }
    }).toList();

    addLog("keywords---$keywords");
    List<String> cipherTextKeyWords = taskModel.map((task) => task.keyword).toList();


    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };


    Map<String, dynamic>? qs = {
      'area': cityId,
      'word': '$keywords',
      'startDate': startData,
      'endDate': endData
    };


    addLog("qs------$qs");

    headers['Cipher-Text'] = getCipherText(cipherTextKeyWords,user.apiKeyTime,user.apiKey);
    headers['Cookie'] = user.cookie;

    // GET请求示例
    try {
      final response = await httpClientUtil.get<Map<String, dynamic>>(
        url: isAnyIndexActive == "consult" ? FeedSearchApi : SearchApi,
        headers: headers,
        queryParams: qs,
        maxRetries: 3,
        proxyAddress: user.isProxy?'${user.proxyAddress}:${user.proxyPort}':null, // 动态设置代理
      );

      addLog("response --- $response");

      var status = response?["status"];
      var message = response?["message"];

      if (status.toString() == '10001'){
        throw Exception(message);
      }

      Map encryptedData = response?["data"];

      if (encryptedData.isNotEmpty){
         var status = response?['status'];
         if(status.toString() == '0'){

           headers.remove('Cipher-Text');
           String uniqid = response?["data"]["uniqid"];
           // addLog(encryptedData);
           String ptbk_url = 'https://index.baidu.com/Interface/ptbk?uniqid=$uniqid';
           String ptbk = "";

           if (user.decryptCache.containsKey(uniqid)) {
             ptbk = user.decryptCache[uniqid]!;
           } else {

             var res = await httpClientUtil.get<Map<String, dynamic>>(
               url: ptbk_url,
               queryParams: {},
               headers: headers,
               proxyAddress: user.isProxy?'${user.proxyAddress}:${user.proxyPort}':null, // 动态设置代理
             );
             ptbk = res!['data'];
             user.decryptCache[uniqid] = ptbk;
           }


           if (isAnyIndexActive == "consult"){
             for (var userIndexesData in encryptedData['index']) {
               String word = userIndexesData['key'].map((item) => item['name']).join('+');
               String encryptedDataAll = userIndexesData['data'];


               List<DateTime> timestampList = List<DateTime>.generate(
                 DateTime
                     .parse(endData)
                     .difference(DateTime.parse(startData))
                     .inDays + 1,
                     (index) =>
                     DateTime.parse(startData).add(Duration(days: index)),
               );

               List<String> dateList = timestampList
                   .map((timestamp) =>
                   DateFormat('yyyy-MM-dd').format(timestamp))
                   .toList();

               List<int> decryptedDataAll = decrypt(ptbk, encryptedDataAll)
                   .split(',')
                   .map((data) => fillZero(data))
                   .toList();

               List<int> generateZeroArray(int length) {
                 // 创建一个长度为 `length` 的列表，每个元素都是 `[0]`
                 return List.generate(length, (_) => 0);
               }
               for (var tasks in taskModel) {
                 addLog("tasks.drama.keyword---${tasks.drama.keyword}");
                 addLog("word---${word}");
                 if(tasks.drama.keyword == word){
                   tasks.drama.all = dateList.length >decryptedDataAll.length ?generateZeroArray(dateList.length):decryptedDataAll;
                   tasks.drama.pc = generateZeroArray(dateList.length);
                   tasks.drama.wise = generateZeroArray(dateList.length);
                   tasks.drama.isCompleted = true;
                   addLog("本次请求成功结束");
                 }
               }

             }


           }else{
             for (var userIndexesData in encryptedData['userIndexes']) {
               String word = userIndexesData['word'].map((item) => item['name']).join('+');
               print(word);

               String encryptedDataAll = userIndexesData['all']['data'];
               String encryptedDataPc = userIndexesData['pc']['data'];
               String encryptedDataWise = userIndexesData['wise']['data'];

               if(encryptedDataWise.isEmpty){
                 encryptedDataAll = encryptedDataPc;
               }
               if(encryptedDataPc.isEmpty){
                 encryptedDataAll = encryptedDataWise;
               }
               if(encryptedDataAll.isEmpty&&encryptedDataPc.isEmpty&&encryptedDataWise.isEmpty){
                 encryptedDataAll = "";
                 encryptedDataPc = "";
                 encryptedDataWise = "";
               }

               List<DateTime> timestampList = List<DateTime>.generate(
                 DateTime
                     .parse(endData)
                     .difference(DateTime.parse(startData))
                     .inDays + 1,
                     (index) =>
                     DateTime.parse(startData).add(Duration(days: index)),
               );

               List<String> dateList = timestampList
                   .map((timestamp) =>
                   DateFormat('yyyy-MM-dd').format(timestamp))
                   .toList();


               List<int> decryptedDataAll = decrypt(ptbk, encryptedDataAll)
                   .split(',')
                   .map((data) => fillZero(data))
                   .toList();
               List<int> decryptedDataPc = decrypt(ptbk, encryptedDataPc)
                   .split(',')
                   .map((data) => fillZero(data))
                   .toList();
               List<int> decryptedDataWise = decrypt(
                   ptbk, encryptedDataWise)
                   .split(',')
                   .map((data) => fillZero(data))
                   .toList();

               List<int> generateZeroArray(int length) {
                 // 创建一个长度为 `length` 的列表，每个元素都是 `[0]`
                 return List.generate(length, (_) => 0);
               }
               for (var tasks in taskModel) {
                 addLog("tasks.drama.keyword---${tasks.drama.keyword}");
                 addLog("word---${word}");
                 if(tasks.drama.keyword == word){
                   tasks.drama.all = dateList.length >decryptedDataAll.length ?generateZeroArray(dateList.length):decryptedDataAll;
                   tasks.drama.pc = dateList.length >decryptedDataPc.length ?generateZeroArray(dateList.length):decryptedDataPc;
                   tasks.drama.wise = dateList.length >decryptedDataWise.length ?generateZeroArray(dateList.length):decryptedDataWise;
                   tasks.drama.isCompleted = true;
                   addLog("本次请求成功结束");
                 }
               }

             }
           }


      }else{
           addLog("object-----$response");
         //    抛出异常 
           // Handle case when the status is not 0 (failure or unexpected status)
           throw Exception(message);
         }
      }

    } catch (e) {
      NotificationUtil.show(
        title: '任务失败',
        body: '当前任务失败。请查看原由！',
      );
      user.isError = true;
      user.isStart = false;
      addLog('错误1: $e');
      setErrorState();
      addLog("❌ 错误-账号:${user.username}---$e");

      update(['list', "two"]);

    }

  }



  // 单个任务处理函数
  Future<void> ppcProcessTask(
      List<TaskModel> taskModel,
      BaiDuUsers user,
      ) async {


    addLog("------------------------");
    addLog("-----单个任务处理函数------");
    addLog("------------------------");



    String startData = taskModel.map((task) => task.drama.startData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String endData = taskModel.map((task) => task.drama.endData).toSet().toString().replaceAll("{", "").replaceAll("}", "");
    String cityId = taskModel.map((task) => task.cityId).toSet().toString().replaceAll("{", "").replaceAll("}", "");

    List<List<String>> keywords = taskModel
        .map((task) => [task.keyword.toString()])
        .toList();


    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };


    Map<String, dynamic>? qs = {
      'regionId': cityId,
      'startDate': startData,
      'endDate': endData,
      "entityId":ppcRegionId[keywords[0][0]],
      "stat": true
    };


    headers['Cookie'] = user.cookie;

    // GET请求示例
    try {
      final response = await httpClientUtil.post<Map<String, dynamic>>(
        url: PPCApi,
        headers: headers,
        data: qs,
        maxRetries: 3,
        proxyAddress: user.isProxy?'${user.proxyAddress}:${user.proxyPort}':null, // 动态设置代理
      );

      addLog("response --- $response");

      var status = response?["status"];
      var message = response?["message"];

      if (status.toString() == '10001'){
        throw Exception(message);
      }

      List encryptedData = response?["data"];

      for (var tasks in taskModel) {
        addLog("tasks.drama.keyword---${tasks.drama.keyword}");
        addLog("word---${keywords[0][0]}");
        if(tasks.drama.keyword == keywords[0][0]){
          tasks.drama.all = encryptedData.map((item) => item['value'] as int).toList();
          tasks.drama.pc = encryptedData.map((item) => item['value'] as int).toList();
          tasks.drama.wise = encryptedData.map((item) => item['value'] as int).toList();
          tasks.drama.isCompleted = true;
          addLog("本次请求成功结束");
        }
      }



    } catch (e) {
      NotificationUtil.show(
        title: '任务失败',
        body: '当前任务失败。请查看原由！',
      );
      user.isError = true;
      user.isStart = false;
      addLog('错误1: $e');
      setErrorState();
      addLog("❌ 错误-账号:${user.username}---$e");

      update(['list', "two"]);

    }

  }



  // 更新数据
  // drama.all = results['all'];
  // drama.pc = results['pc'];
  // drama.wise = results['wise'];
  // drama.isCompleted = true;


  // 按年份收集未完成任务
  Map<String, List<TaskModel>> collectUncompletedTasksByYear(List<DataModel> data) {
    Map<String, List<TaskModel>> tasksByYear = {};

    for (var dataModel in data) {
      if (dataModel.id == null) continue;

      dataModel.data?.forEach((year, dramaList) {
        if (!tasksByYear.containsKey(year)) {
          tasksByYear[year] = [];
        }

        for (var drama in dramaList) {
          if (drama.isCompleted != true) {
            tasksByYear[year]!.add(TaskModel(
              dataModel: dataModel,
              keyword: drama.keyword!,
              drama: drama,
              year: year,
              cityId: dataModel.id!,
              region: dataModel.region!,
            ));
          }
        }
      });
    }

    return tasksByYear;
  }

  // 停止任务
  void onBdStop() {
    if (taskState == TaskState.running || taskState == TaskState.error) {
      setStoppedState();
      addLog('⏹️ 任务已停止');
      update(['buttons']);
      // 停止任务的逻辑
    }
  }

  // 继续任务
  void onBdContinue() async{
    if (taskState == TaskState.error || taskState == TaskState.stopped) {
      addLog('✅️ 任务开始继续');
      try {
        addLog("走到这里了吗");
        taskState = TaskState.running;
        await distributeTasksToUsers();  // 不需要传参，直接使用类中的 data
      } catch (e) {
        addLog('任务分发失败: $e');
       taskState = TaskState.error;
      }
      update(['buttons']);

      // 继续任务的逻辑
    }
  }

  // 设置继续状态
  void setErrorState() {
    if (taskState == TaskState.running) {
      taskState = TaskState.error;
      update(['buttons']);
    }
  }
  //设置成开始状态
  void setInitialState() {
    taskState = TaskState.initial;
    update(['buttons']);
  }
  //设置成停止状态
  void setStoppedState() {
    taskState = TaskState.stopped;
    update(['buttons']);
  }







  Future<bool> checkProxy(String proxyAddress, String proxyPort, [String? username, String? password]) async {
    try {
      final HttpClient client = HttpClient();

      // 设置代理
      client.findProxy = (uri) {
        if (username != null && username.isNotEmpty) {
          return 'PROXY $username:$password@$proxyAddress:$proxyPort';
        }
        return 'PROXY $proxyAddress:$proxyPort';
      };

      // 设置超时
      client.connectionTimeout = Duration(seconds: 10);

      // 测试连接（使用百度作为测试网站）
      final request = await client.getUrl(Uri.parse('https://www.baidu.com'));
      final response = await request.close();

      // 关闭客户端
      client.close();

      // 检查响应状态
      return response.statusCode == 200;
    } catch (e) {
      addLog('代理检测错误: $e');
      addLog("× 代理检测错误:$e");

      return false;
    }
  }


//  日志相关
  List<String> logs = [];
  // 添加日志的ScrollController
  final ScrollController logScrollController = ScrollController();


  void addLog(String message) {
// 在开头插入新日志
    logs.insert(0, "[${DateTime.now().toString().split('.')[0]}] $message");

    // 如果超过500条，删除最老的日志（列表末尾的）
    if (logs.length > 500) {
      logs.removeRange(500, logs.length);  // 修改这里：删除500条之后的
    }

    update(['logs', 'logs_count']);

    // 优化滚动逻辑
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (logScrollController.hasClients) {
        logScrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void clearLogs() {
    logs.clear();
    update(['logs','logs_count']);
  }

  /// 延迟倒计时函数
  Future<void> delayWithCountdown({required String? seconds,}) async {
    try {
      // 确定延迟时间
      int delaySeconds = 1;
      if (seconds != null && seconds.isNotEmpty) {
        delaySeconds = int.tryParse(seconds) ?? 1;
        addLog('⚡ 设置任务间隔: $delaySeconds 秒', );
      } else {
        addLog('⚡ 使用默认间隔: 1秒',);
      }
      update(['logs']);

      // 执行倒计时
      for (int i = delaySeconds; i > 0; i--) {
        addLog('⏱️ 等待中: 还剩 $i 秒...',);
        update(['logs']);
        await Future.delayed(Duration(seconds: 1));
      }

      // 倒计时结束
      addLog('✨ 开始执行新的任务...',);
      update(['logs']);

    } catch (e) {
      addLog('❌ 任务间隔出错: $e',);
      update(['logs']);
    }
  }



//  解密
  // 解密函数
  String decrypt(String ptbk, String encryptedData) {
    // 这里假设解密过程仅为演示目的，请根据实际情况实现
    if (ptbk.isEmpty || encryptedData.isEmpty) {
      return "";
    }
    int n = ptbk.length ~/ 2;
    Map<String, String> d = {for (int o = 0; o < n; o++) ptbk[o]: ptbk[n + o]};
    List<String> decryptedData =
    encryptedData.split('').map((data) => d[data] ?? "").toList();
    return decryptedData.join("");
  }

// 数据填充零
  int fillZero(String data) {
    return data.isEmpty ? 0 : int.parse(data);
  }


//  数据处理
  Future<void> handlingData2() async {
    String jsonString = json.encode(data.map((e) => e.toJson()).toList());
    // addLog(jsonString);

    List<TreeNode> treeNodes = flattenCheckedNodes(area_data);

    for (var keyword in keyWords) {
      addLog(keyword);

      for (TreeNode treeNode in treeNodes) {
        addLog(treeNode.name);
        if(treeNode.name.isEmpty){
          addLog("× 错误-城市为空");

          return;
        }


        // 创建各个时间段的数据集合
        List<List<dynamic>> dailyData = [];
        List<List<dynamic>> monthlyData = [];
        List<List<dynamic>> weeklyData = [];
        List<List<dynamic>> yearlyData = [];

        var matchingDataModels = data.where((dataModel) => dataModel.region == treeNode.name);

        addLog("✨ 开始保存数据-地区${treeNode.name}-关键词$keyword");

        // 处理每个数据项
        var futures = <Future>[];

        for (var dataModel in matchingDataModels) {
          futures.add(Future(() async {
            await _processDataModel(dataModel, keyword, treeNode, dailyData, monthlyData, weeklyData, yearlyData);
          }));
        }

        // 等待所有异步操作完成
        await Future.wait(futures);

        // 添加标题行并处理列的删除
        _addHeadersAndRemoveColumns(dailyData, "日");
        _addHeadersAndRemoveColumns(monthlyData, "月");
        _addHeadersAndRemoveColumns(weeklyData, "周");
        _addHeadersAndRemoveColumns(yearlyData, "年");

        // 生成并保存 CSV 文件
        String path = await StoreUtil.checkAndCreateFolders(treeNode.name, keyword);

       if(r){
         // 按日保存
         String dailyFileName = "${treeNode.name}-日-$keyword-$startDate-$endDate";
         exportCsv(dailyData, path, dailyFileName);
         addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按日");
       }

        if(y){
          // 按月保存
          String monthlyFileName = "${treeNode.name}-月-$keyword-$startDate-$endDate";
          exportCsv(monthlyData, path, monthlyFileName);
          addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按月");
        }

        if(z){
          // 按周保存
          String weeklyFileName = "${treeNode.name}-周-$keyword-$startDate-$endDate";
          exportCsv(weeklyData, path, weeklyFileName);
          addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按周");
        }

        if(n){
          // 按年保存
          String yearlyFileName = "${treeNode.name}-年-$keyword-$startDate-$endDate";
          exportCsv(yearlyData, path, yearlyFileName);
          addLog("✅ 保存数据成功-地区${treeNode.name}-关键词$keyword - 按年");
        }


      }
    }
    NotificationUtil.show(
      title: '数据保存成功',
      body: '已保存所有关键词的数据，请在运行目录查看！',
    );
  }

  Future<void> handlingData0() async {
    // 1. 预先获取所需数据，避免重复调用
    final treeNode = flattenCheckedNodes(area_data);
    final yearDetails = daysInEachYear(startDate, endDate);
    
    // 2. 使用并发处理生成每日数据
    final _data = await genreteDailyData();
    
    // 3. 优化数据查找 - 使用Map存储，提高查找效率
    final dataMap = {
      for (var item in _data)
        "${item[0]}_${item[1]}_${item[2]}": item  // key: keyword_region_date
    };

    // 4. 并发处理每年的数据
    await Future.wait<void>(
      yearDetails.map((yearDetail) {
        return Future<void>.sync(() async {
          // 5. 优化内存分配 - 预分配数组大小
          final dailyData = [["日期", "地区"]];
          for (var keyword in keyWords) {
            dailyData[0].addAll(["${keyword}_全部", "${keyword}_PC", "${keyword}_Wise"]);
          }

          // 6. 优化日期处理
          final dates = _generateDateRange(yearDetail.start, yearDetail.end);
          
          // 7. 预分配行数据
          final rowCount = dates.length * treeNode.length;
          final initialRows = List.generate(
            rowCount,
            (index) {
              final dateIndex = index ~/ treeNode.length;
              final nodeIndex = index % treeNode.length;
              return [dates[dateIndex], treeNode[nodeIndex].name];
            }
          );
          dailyData.addAll(initialRows);

          // 8. 批量处理数据
          var processedCount = 0;
          final totalCount = dates.length * treeNode.length * keyWords.length;
          
          // 9. 使用批量更新减少UI更新频率
          for (var date in dates) {
            for (var node in treeNode) {
              for (var keyword in keyWords) {
                final key = "${keyword}_${node.name}_$date";
                final result = dataMap[key];
                
                if (result != null) {
                  final row = dailyData.firstWhere(
                    (row) => row[0] == date && row[1] == node.name,
                    orElse: () => [],
                  );
                  
                  if (row.isNotEmpty) {
                    row.addAll([
                      result[3]?.toString() ?? '0',
                      result[4]?.toString() ?? '0',
                      result[5]?.toString() ?? '0'
                    ]);
                  }
                }
                
                processedCount++;
                if (processedCount % 100 == 0) { // 每处理100条数据更新一次进度
                  addLog("⏳ 处理进度: ${(processedCount / totalCount * 100).toStringAsFixed(1)}%");
                }
              }
            }
          }

          // 10. 移除空行
          dailyData.removeWhere((row) => 
            row.length <= 2 || row.skip(2).every((value) => value == null)
          );

          // 11. 保存数据
          final year = yearDetail.start.substring(0, 4);
          final resultFolderPath = await StoreUtil.checkAndCreateFolders('', '数据提取结果');
          exportCsv(dailyData, resultFolderPath, year);
          addLog("✅ 保存数据成功 - $year年度数据");
        });
      }).toList()
    );

    NotificationUtil.show(
      title: '数据保存成功',
      body: '已保存所有关键词的数据，请在运行目录查看！',
    );
  }

  // 辅助函数：生成日期范围
  List<String> _generateDateRange(String start, String end) {
    final startDate = DateTime.parse(start);
    final endDate = DateTime.parse(end);
    return List.generate(
      endDate.difference(startDate).inDays + 1,
      (index) => DateFormat('yyyy-MM-dd').format(startDate.add(Duration(days: index)))
    );
  }

  List<dynamic>? findRecord(List<List<dynamic>> data, List<dynamic> target) {
    return data.firstWhere(
          (row) => row[0] == target[0] && row[1] == target[1] && row[2] == target[2],
      orElse: () => [], // 如果没有找到匹配项，返回 null
    );
  }


  Future<List<List>> genreteDailyData()async{
    List<TreeNode> treeNodes = flattenCheckedNodes(area_data);
    // 创建各个时间段的数据集合
    List<List<dynamic>> dailyData = [];

    for (var keyword in keyWords) {
      addLog(keyword);

      for (TreeNode treeNode in treeNodes) {
        addLog(treeNode.name);
        if(treeNode.name.isEmpty){
          addLog("× 错误-城市为空");

          return [[]];
        }



        List<List<dynamic>> monthlyData = [];
        List<List<dynamic>> weeklyData = [];
        List<List<dynamic>> yearlyData = [];

        var matchingDataModels = data.where((dataModel) => dataModel.region == treeNode.name);


        // 处理每个数据项
        var futures = <Future>[];

        for (var dataModel in matchingDataModels) {
          futures.add(Future(() async {
            await _processDataModel(dataModel, keyword, treeNode, dailyData, monthlyData, weeklyData, yearlyData);
          }));
        }

        // 等待所有异步操作完成
        await Future.wait(futures);

        // 添加标题行并处理列的删除
        _addHeadersAndRemoveColumns(dailyData, "日");
        _addHeadersAndRemoveColumns(monthlyData, "月");
        _addHeadersAndRemoveColumns(weeklyData, "周");
        _addHeadersAndRemoveColumns(yearlyData, "年");




      }
    }

    return dailyData;

  }

// 处理数据模型的方法
  Future<void> _processDataModel(DataModel dataModel, String keyword, TreeNode treeNode,
      List<List<dynamic>> dailyData, List<List<dynamic>> monthlyData,
      List<List<dynamic>> weeklyData, List<List<dynamic>> yearlyData) async {
    for (var entry in dataModel.data!.entries) {
      // 解构 entry，直接使用 entry.key 和 entry.value
      var year = entry.key;  // 获取年份
      var dramas = entry?.value;  // 获取对应年份的所有 dramas

      for (var drama in dramas!) {
        if (drama.keyword == keyword) {
          // addLog('Found match: Region = ${dataModel.region}, Year = $year, Drama = ${drama.toJson()}');

          DateTime startDate = DateTime.parse(drama.startData ?? '');
          DateTime endDate = DateTime.parse(drama.endData ?? '');

          // 生成日期范围
          List<DateTime> timestampList = List<DateTime>.generate(
            endDate.difference(startDate).inDays + 1,
                (index) => startDate.add(Duration(days: index)),
          );
          List<String> dateList = timestampList
              .map((timestamp) => DateFormat('yyyy-MM-dd').format(timestamp))
              .toList();



          if(r){
            // 按日处理
            dailyData.addAll(_processDailyData(drama, dataModel.region!, dateList));
          }
          if (y){
            // 按月加总
            monthlyData.addAll(await _processMonthlyData(drama, dataModel.region!, dateList, timestampList));
          }
          if (z){
            // 按周加总
            weeklyData.addAll(await _processWeeklyData(drama, dataModel.region!, dateList, timestampList));
          }

          if (n){
            // 按年加总
            yearlyData.addAll(await _processYearlyData(drama, dataModel.region!, dateList, timestampList));
          }


        }
      }
    }
  }

  List<List<dynamic>> _processDailyData(
      Drama drama, String region, List<String> dateList) {
    addLog("drama--${drama.pc?.length}");
    addLog("drama--${drama.pc}");
    addLog("dateList--${dateList.length}");

    List<List<dynamic>> dailyData = [];
    for (int i = 0; i < dateList.length; i++) {
      dailyData.add([
        drama.keyword,
        region,
        dateList[i],
        drama.all?[i],
        drama.pc?[i],
        drama.wise?[i],
      ]);
    }
    return dailyData;
  }

  Future<List<List<dynamic>>> _processMonthlyData(
      Drama drama, String region, List<String> dateList, List<DateTime> timestampList) async {
    Map<String, List<num>> monthlySum = {};
    for (int i = 0; i < dateList.length; i++) {
      String month = DateFormat('yyyy-MM').format(timestampList[i]);
      if (!monthlySum.containsKey(month)) {
        monthlySum[month] = [0, 0, 0];
      }
      monthlySum[month]![0] += drama.all?[i] ?? 0;
      monthlySum[month]![1] += drama.pc?[i] ?? 0;
      monthlySum[month]![2] += drama.wise?[i] ?? 0;
    }

    List<List<dynamic>> monthlyData = [];
    monthlySum.forEach((month, sums) {
      addLog("1sums----,$sums");
      DateTime startMonth = DateTime.parse(month + '-01');
      DateTime endMonth = DateTime(startMonth.year, startMonth.month + 1, 0);
      // endMonth.day

      String monthRange = "${DateFormat('yyyy-MM-dd').format(startMonth)} ~ ${DateFormat('yyyy-MM-dd').format(endMonth)}";
      if(sjjz){
        monthlyData.add([
          drama.keyword,
          region,
          monthRange,
          sums[0].toInt(),
          sums[1].toInt(),
          sums[2].toInt(),
        ]);
      }
      if(sjpj){
        monthlyData.add([
          drama.keyword,
          region,
          monthRange,
          (sums[0].toInt()/endMonth.day),
          (sums[1].toInt()/endMonth.day),
          (sums[2].toInt()/endMonth.day),
        ]);
      }
    });

    return monthlyData;
  }

  Future<List<List<dynamic>>> _processWeeklyData(
      Drama drama, String region, List<String> dateList, List<DateTime> timestampList) async {
    Map<String, List<num>> weeklySum = {};
    for (int i = 0; i < dateList.length; i++) {
      DateTime monday = getStartOfWeek(timestampList[i]);
      String week = DateFormat('yyyy-MM-dd').format(monday);
      if (!weeklySum.containsKey(week)) {
        weeklySum[week] = [0, 0, 0];
      }
      weeklySum[week]![0] += drama.all?[i] ?? 0;
      weeklySum[week]![1] += drama.pc?[i] ?? 0;
      weeklySum[week]![2] += drama.wise?[i] ?? 0;
    }

    List<List<dynamic>> weeklyData = [];
    weeklySum.forEach((week, sums) {
      DateTime monday = DateTime.parse(week);
      DateTime sunday = monday.add(Duration(days: 6));
      // DateTime weekStartDate = getStartOfWeek(timestampList[0], int.parse(week));
      // DateTime weekEndDate = weekStartDate.add(Duration(days: 6));
      // String weekRange = "${DateFormat('yyyy-MM-dd').format(weekStartDate)} ~ ${DateFormat('yyyy-MM-dd').format(weekEndDate)}";
      String weekRange = "${DateFormat('yyyy-MM-dd').format(monday)}~${DateFormat('yyyy-MM-dd').format(sunday)}";
      print("weekRange--$weekRange sums[0]--${sums[0]}  sums[1]--${sums[1]}  sums[2]--${sums[2]}");
      if(sjjz){
        weeklyData.add([
          drama.keyword,
          region,
          weekRange,
          sums[0].toInt(),
          sums[1].toInt(),
          sums[2].toInt(),
        ]);
      }
      if(sjpj){
        weeklyData.add([
          drama.keyword,
          region,
          weekRange,
          sums[0].toInt()/7,
          sums[1].toInt()/7,
          sums[2].toInt()/7,
        ]);
      }

    });

    return weeklyData;
  }

  int getDaysInYear(int year) {
    // Leap year check
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
      return 366; // Leap year
    } else {
      return 365; // Regular year
    }
  }
  Future<List<List<dynamic>>> _processYearlyData(
      Drama drama, String region, List<String> dateList, List<DateTime> timestampList) async {
    Map<String, List<num>> yearlySum = {};
    for (int i = 0; i < dateList.length; i++) {
      String year = DateFormat('yyyy').format(timestampList[i]);
      if (!yearlySum.containsKey(year)) {
        yearlySum[year] = [0, 0, 0];
      }
      yearlySum[year]![0] += drama.all?[i] ?? 0;
      yearlySum[year]![1] += drama.pc?[i] ?? 0;
      yearlySum[year]![2] += drama.wise?[i] ?? 0;
    }

    List<List<dynamic>> yearlyData = [];
    yearlySum.forEach((year, sums) {

      if (sjjz){
        yearlyData.add([
          drama.keyword,
          region,
          year,
          sums[0].toInt(),
          sums[1].toInt(),
          sums[2].toInt(),
        ]);
      }



      if (sjpj){
        int currentYear = getDaysInYear(int.parse(year));
        yearlyData.add([
          drama.keyword,
          region,
          year,
          sums[0].toInt()/currentYear,
          sums[1].toInt()/currentYear,
          sums[2].toInt()/currentYear,
        ]);
      }


    });

    return yearlyData;
  }

// 添加表头并删除不需要的列
  void _addHeadersAndRemoveColumns(List<List<dynamic>> data, String type) {
    if (data.isNotEmpty && data[0][0] != "关键词") {
      switch (type) {
        case "日":
          data.insert(0, ["关键词", "地区", "日期", "全部", "PC端", "移动端"]);
          break;
        case "月":
          data.insert(0, ["关键词", "地区", "月份", "全部", "PC端", "移动端"]);
          break;
        case "周":
          data.insert(0, ["关键词", "地区", "周次", "全部", "PC端", "移动端"]);
          break;
        case "年":
          data.insert(0, ["关键词", "地区", "年份", "全部", "PC端", "移动端"]);
          break;
      }

      // 调用方法删除相应的列
      removeColumnsIfNeeded(data, AnyIndexActives);
    }
  }

  List<String> generateIndexList(List<dynamic> indexActives) {
    print("indexActives---$indexActives");

    List<String> result = [];

    // 判断每个布尔值，决定是否添加对应的字符串
    if (!indexActives[0]) {
      result.add("全部");
    }
    if (!indexActives[1]) {
      result.add("PC端");
    }
    if (!indexActives[2]) {
      result.add("移动端");
    }
    print("result---$result");

    return result;
  }

  void removeColumnsIfNeeded(List<List<dynamic>> data, List<dynamic> columnsToRemove) {
    // 调用函数生成最终的列表
    List<String> activeIndexes = generateIndexList(columnsToRemove);

    // 遍历列名列表
    for (var columnName in activeIndexes) {

      // 如果列名存在于表头
      if (data[0].contains(columnName.toString())) {
        // 调用删除列的方法
        removeColumn(data, columnName.toString());
      }
    }
  }

  void removeColumn(List<List<dynamic>> data, String columnName) {
    // 找到目标列的索引
    int columnIndex = data[0].indexOf(columnName);

    if (columnIndex != -1) {
      // 删除每行对应索引的列
      for (var row in data) {
        row.removeAt(columnIndex);
      }
    }
  }

// 获取给定年份中的第几周的开始日期
  DateTime getStartOfWeek(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  int getWeekOfYear(DateTime date) {
    final isoDate = date.toIso8601String().substring(0, 10);
    final weekYear = _getWeekYear(date);
    return int.parse((isoDate.split("-")[1] == '01' && weekYear != date.year
        ? 53 // 跨年周特殊处理
        : (date.difference(DateTime(date.year, 1, 1)).inDays / 7).ceil()) as String);
  }

  // 辅助函数：获取ISO周所在年
  int _getWeekYear(DateTime date) {
    final thursday = date.add(Duration(days: DateTime.thursday - date.weekday));
    return thursday.year;
  }

  void exportCsv(List<List<dynamic>> csvData, String path, String fileName) async {
    String csv = const ListToCsvConverter().convert(csvData);

    // 添加 UTF-8 BOM
    List<int> bom = [0xEF, 0xBB, 0xBF];
    List<int> csvBytes = utf8.encode(csv);
    List<int> bomCsvBytes = bom + csvBytes;

    // 组合路径和文件名
    final File file = File('$path/$fileName.csv');

    // 将CSV数据写入文件
    await file.writeAsBytes(bomCsvBytes);
  }


//  检测按钮
  onCheckExit()async{

    Map<String, String> headers = {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Encoding': 'gzip, deflate, br',
//     'Cipher-Text':"""
// 1719227393545_1719297883463_ru+UprB9gpwT7WwiWAVjCtCYF62iacritHFdi6xzdQoN77ErmFRqYmuQ8xwK2qzOPkkPNx4Kv6Or2YzDFcLMcqk5MXxWGoKIXIzzAMi8mHpVJRCMRswfXd1QWapnnAQsIbw4E2HRHIdBpfH6+uINhE4r5InHgSLkjjioFdGNAMps0ZNoa+S9dJgEzlItL46LBMdZQBeryvasC8GUNlUNlka8wf9uxFTlD/LsOc/qsgn/Uj+lMzTjNqMomYWiXVDqxntXKEW7TVEVva8AXzq4yQzlSsuL9xjFWqTydkoXtVQ6zgYgoB0IIooW+/jgWQQrzNqpOjbbMlXDpkFJDNZ+LnwlvEfSAEWKJTbzEbWvdP8UK3wee9zTUlOWtL7ATPjfDtLQEVt0XLCoGEy/OamDI09GfwkcOG2e/B2bdwtNsDu/N2fa2NZkU3uvObHvAzCt""",
      'Connection': 'close',
      'Host': 'index.baidu.com',
      'Referer': 'https://index.baidu.com/v2/main/index.html',
      'User-Agent':"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 Core/1.116.438.400 QQBrowser/13.0.6071.400"
    };

    // 要删除的关键词列表
    List<String> toRemove = [];
    List<String> errorList = [];
    List<String> ppcList = [];

     for(var keyword in keyWords){
       addLog('📍 正在检测关键词: $keyword', );

       addLog("keyword--$keyword");

       String wordSugUrl = "https://index.baidu.com/insight/word/sug";

       if (keyword.contains('+')) {
          var k = keyword.split('+');
          for(var k_i in k){
            addLog(k_i);
            headers['Cookie'] = users[0].cookie;
            var response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[k_i],"source":"pc_home"});
            // 调用函数，检查是否存在 '旅游' 这个 word
            bool exists = checkWordInResult(response, k_i);
            addLog("exists ----- $exists");  // 输出: true
            if (!exists) {
              // 如果没有找到，添加到删除列表
              toRemove.add(keyword);
            }

          }
       }else{
         headers['Cookie'] = users[0].cookie;
         var response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[keyword],"source":"pc_home"});
         addLog("response --- $response");
         var status = response['status'];
         bool exists;
         bool ppcexists;
         if(status != 0){
           // response = await httpClientUtil.post(url: wordSugUrl,headers: headers,data:{"words":[keyword],"source":"pc_home"});
           errorList.add(keyword);
           // exists = checkWordInResult(response, keyword);
           addLog("1");
           continue;

         }else{
           exists = checkWordInResult(response, keyword);
           ppcexists = checkPPcWordInResult(response, keyword);
            addLog("2");

         }
         print(exists);
         print(ppcexists);

         if (exists) {
           toRemove.add(keyword);
         }

         if (ppcexists) {
           // 如果没有找到，添加到删除列表
           ppcList.add(keyword );
         }
       }
     }
    // 删除未找到的关键词
    // keyWords.removeWhere((keyword) => toRemove.contains(keyword));
    // 定义文件路径
    String filePath = 'keywords.txt';
    String ppcfilePath = 'ppckeywords.txt';
    String efilePath = 'errKeywords.txt';

    print(toRemove);
    print(errorList);
    print(ppcList);
    // 创建文件对象
    File file = File(filePath);
    File ppcfile = File(ppcfilePath);
    File efile = File(efilePath);

    // 写入数据到文件
    await file.writeAsString('');  // 先清空文件内容，如果文件已存在
    await ppcfile.writeAsString('');  // 先清空文件内容，如果文件已存在
    await efile.writeAsString('');  // 先清空文件内容，如果文件已存在

    // 将每个关键词写入文件，每个关键词占一行
    for (var keyword in toRemove) {
      await file.writeAsString('$keyword\n', mode: FileMode.append);
    }
    for (var ppckeyword in ppcList) {
      await ppcfile.writeAsString('$ppckeyword\n', mode: FileMode.append);
    }
    for (var ekeyword in errorList) {
      await efile.writeAsString('$ekeyword\n', mode: FileMode.append);
    }

    addLog('数据已写入到 $filePath 文件');
    NotificationUtil.show(
      title: '更新关键词成功',
      body: '已更新所有关键词的数据，''数据已写入到 $filePath 文件',
    );
  }

  bool checkWordInResult(Map<String, dynamic> data, String wordToCheck) {

    // 获取 data 中的 result 列表
    List<dynamic> result = data['data']['result'];

    // 遍历 result 列表
    for (var item in result) {
      // 检查 word 是否匹配
      if (item['word'] == wordToCheck && item['type'] == "word") {
        print("找到匹配的word");
        return true;  // 找到匹配的 word，返回 true
      }
    }

    // 如果遍历完没有找到匹配的 word，返回 false
    return false;
  }

  bool checkPPcWordInResult(Map<String, dynamic> data, String wordToCheck) {

    // 获取 data 中的 result 列表
    List<dynamic> result = data['data']['result'];

    // 遍历 result 列表
    for (var item in result) {
      // 检查 word 是否匹配
      if (item['word'] == wordToCheck && item['type'] == "brand") {
        print("找到匹配的brand");
        return true;  // 找到匹配的 word，返回 true
      }
    }

    // 如果遍历完没有找到匹配的 word，返回 false
    return false;
  }

  // 批量导入代理
  Future<void> importProxies(List<ProxyConfig> proxies, int validTime) async {
    int successCount = 0;
    int failCount = 0;
    
    final selectedUsers = getSelectedUsers();
    if (selectedUsers.isEmpty) {
      showToast("请先选择要设置代理的账号");
      return;
    }
    
    for (int i = 0; i < selectedUsers.length && i < proxies.length; i++) {
      var user = selectedUsers[i];
      if (!user.isStart) {  // 只给未启用的账号设置代理
        bool isValid = await checkProxy(
          proxies[i].ip,
          proxies[i].port,
          proxies[i].username,
          proxies[i].password,
        );
        
        if (isValid) {
          user.proxyAddress = proxies[i].ip;
          user.proxyPort = proxies[i].port;
          user.proxyUsername = proxies[i].username;
          user.proxyPassword = proxies[i].password;
          user.proxyValidTime = validTime;
          user.proxyStartTime = DateTime.now();
          user.isProxy = true;
          successCount++;
        } else {
          failCount++;
        }
      }
    }
    
    update(['list', 'now', 'three']);
    addLog("✅ 批量导入代理完成 - 成功:$successCount 失败:$failCount");
  }

  List<BaiDuUsers> getSelectedUsers() {
    return users.where((user) => user.isSelected).toList();
  }

  bool get isAllSelected => users.isNotEmpty && users.every((user) => user.isSelected);
  
  void toggleAllSelection(bool value) {
    for (var user in users) {
      user.isSelected = value;
    }
    update(['list']);
  }

  void toggleUserSelection(int index, bool value) {
    users[index].isSelected = value;
    update(['list']);
  }


  // void ppcDistributeTasksToUsers(){
  //
  // }
}

// 代理配置模型
class ProxyConfig {
  final String ip;
  final String port;
  final String? username;
  final String? password;
  
  ProxyConfig({
    required this.ip,
    required this.port,
    this.username,
    this.password,
  });
  
  // 从字符串解析代理配置
  static ProxyConfig? fromString(String text) {
    final parts = text.trim().split(':');
    if (parts.length >= 2) {
      return ProxyConfig(
        ip: parts[0],
        port: parts[1],
        username: parts.length > 2 ? parts[2] : null,
        password: parts.length > 3 ? parts[3] : null,
      );
    }
    return null;
  }
}

class TreeNode {
  final String name;
  final int id;
  bool isChecked;
  List<TreeNode> children;

  TreeNode(this.name, this.id, this.children, {this.isChecked = false});
}


